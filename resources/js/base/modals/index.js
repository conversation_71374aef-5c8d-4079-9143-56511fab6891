import { exists } from "../../utils/helpers";
import { createModal, closeModal } from "../../utils/functions";

const modal = (function modal() {
  var $modalTrigger = $("body").find('[data-trigger="modal"]');
  var $overlay = $("body").find("#overlay");

  if (exists($modalTrigger)) {
    $modalTrigger.on("click", function (e) {
      e.preventDefault();

      // Show overlay
      if (exists($overlay)) {
        $overlay.addClass("active");

        // Now lets make the modal content

        // Lets see if its a iframe
        var $url = $(this).attr("href");

        if ($url) {
          var $referrer =
            $(this).data("referrer") ||
            window.location.protocol +
              "//" +
              window.location.host +
              "/" +
              window.location.pathname;

          var $theme = $(this).data("theme")
            ? "modal-" + $(this).data("theme")
            : "modal";

          createModal(
            `<iframe class="iframe-modal" src="${$url}?modal=true&theme=${$theme}&referrer=${$referrer}"></iframe>`,
            $theme
          );
        }
        // Lets see if its pointing to other content
        else if ($(this).data("target")) {
          let $getContent = $(this).data("target");
          let $content = $("body")
            .find("#" + $getContent)
            .html();

          createModal($content);
        }
        // If not, lets grab the manual content and make it
        else {
          let $content = $(this).html();

          createModal($content);
        }
      }

      // Scroll to modal when opened
      $("html, body").animate(
        {
          scrollTop: $("body").offset().top - 50,
        },
        500
      );
    });
  } // end if exists

  // Now lets close the modal
  closeModal();

  // If user clicks off modal, we want to close it
  $(document).on("click", "#overlay", function () {
    closeModal();
  });

  // Close if close icon is pressed
  $(document).on("click", "#modal-close", function () {
    closeModal();
  });

  // If the user presses esc, we also want to close modal
  $(document).on("keyup", function (e) {
    if (e.keyCode == 27) {
      closeModal();
    }
  });
})();

export default modal;
