import { exists } from "../../utils/helpers";

const obscureEmails = (function obscureEmails() {
  var $email = $("body").find(".email");

  if (exists($email)) {
    $email.each(function () {
      var $encEmail = $(this).attr("href");

      $(this).attr("href", "mailto:".concat(atob($encEmail)));

      if ($(this).hasClass("has-icon")) {
        var $icon = $(this).find(".icon");
        console.log($icon);
        $(this).text(" " + atob($encEmail));
        $(this).prepend($icon);
      } else {
        $(this).text(atob($encEmail));
      }
    });
  }
})();

export default obscureEmails;
