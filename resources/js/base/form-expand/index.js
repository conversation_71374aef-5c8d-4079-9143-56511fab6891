import { exists } from "../../utils/helpers";

const formExpand = (function formExpand() {
  var $form = $("#sidebar").find(".wpcf7-form");
  var $formExpand = $form.find(".form-expand");

  $formExpand.hide();

  if (exists($form)) {
    // Create a cross - cross created because if modal forms/page forms etc easier this way
    var $close = `<div class="form-close" id="form-close" aria-label="Close the expanded form">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>`;

    // Add to form
    $form.prepend($close);

    var $formClose = $form.find("#form-close");

    // Slide the form down on click of the form
    $form.find(".field-row").on("click", function () {
      $formExpand.slideDown("fast");
      $formClose.show();
    });

    // Close the form
    $formClose.on("click", function () {
      $formExpand.slideUp("fast");
      $(this).hide();
    });
  }
})();

export default formExpand;
