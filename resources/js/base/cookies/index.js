import { setCookie, getCookie, closeModal } from "../../utils/functions";
import { exists } from "../../utils/helpers";

const allowCookies = (function allowCookies() {
  var $modalTrigger = $("body").find('[data-trigger="cookies"]');
  var $cookieModal = $("body").find("#cookie-modal");
  var $cookieBar = $("body").find("#cookie-bar");
  var $getPermission = getCookie("wordpress_cookiepref");
  var $gaSwitch = $cookieModal.find("#ga-cookie-switch");
  var $tpSwitch = $cookieModal.find("#tp-cookie-switch");
  var $cookieCustom = $("body").find(".custom-cookies");
  var $cookieReject = $("body").find(".reject-cookies");
  var $cookieAllow = $("body").find(".allow-cookies"); // looks in the footer for both instances if .allow-cookies
  var $overlay = $("body").find("#overlay");
  // var $noConsentMessage = "Warning: This website will not work as intended with your current cookie settings.";
  window.dataLayer = window.dataLayer || [];

  //
  // Visual aspect
  //

  // Lets open the modal
  if (exists($modalTrigger)) {
    $modalTrigger.on("click touch", function (e) {
      e.preventDefault();

      // Make the cookie modal visible
      $("body").find($overlay).addClass("active");
      $cookieBar.hide();
      $cookieModal.show();

      // Scroll to modal when opened
      $("html, body").animate(
        {
          scrollTop: $("body").offset().top - 50,
        },
        500
      );
    });
  }

  // If user wants to change their permissions, this makes the cookie bar re-appear
  var $cookieLink = $("#footer").find("#menu-item-174");

  $cookieLink.on("click touch", function (e) {
    e.preventDefault();

    $cookieBar.show();
  });

  // Make checkboxes checked or not
  if (
    $getPermission == null ||
    $getPermission == undefined ||
    $getPermission == ""
  ) {
    // If they have not set cookies yet:

    // Make cookie bar visible
    $cookieBar.show();
    // We also want to set the switches in the modal to 'ON'
    $gaSwitch.prop("checked", true);
    $tpSwitch.prop("checked", true);
  } else if ($getPermission == "all") {
    $gaSwitch.prop("checked", true);
    $tpSwitch.prop("checked", true);
    // If cookies have been set, hide the bar
    $cookieBar.hide();
  } else if ($getPermission == "ga") {
    $gaSwitch.prop("checked", true);
    $tpSwitch.prop("checked", false);
    // If cookies have been set, hide the bar
    $cookieBar.hide();
  } else if ($getPermission == "tp") {
    $gaSwitch.prop("checked", false);
    $tpSwitch.prop("checked", true);
    // If cookies have been set, hide the bar
    $cookieBar.hide();
  } else if ($getPermission == "none") {
    $gaSwitch.prop("checked", false);
    $tpSwitch.prop("checked", false);

    $cookieBar.hide();
    // We want to keep the bar visible with warning message if no permission <- CHANGED MIND
    // $cookieBar.show();
    // $cookieBar.find('.cookie-message').text($noConsentMessage);
  }

  // Plain english console log user cookie permissions
  if ($getPermission == "all") {
    window.dataLayer.push({ event: "all-cookies-allowed" });
    console.log("Cookie permissions: All cookies allowed");
  } else if ($getPermission == "ga") {
    window.dataLayer.push({ event: "ga-cookies-allowed" });
    console.log("Cookie permissions: Google Analytics cookies allowed");
  } else if ($getPermission == "tp") {
    window.dataLayer.push({ event: "tp-cookies-allowed" });
    console.log("Cookie permissions: Third Party cookies allowed");
  } else if ($getPermission == "none") {
    window.dataLayer.push({ event: "" });
    console.log("Cookie permissions: Essential cookies only");
  } else {
    window.dataLayer.push({ event: "" });
    console.log("Cookie permissions: Cookies not consented yet");
  }

  //
  // Functionality
  //

  // If the user accepts via the cookie bar
  $cookieAllow.on("click touch", function (e) {
    e.preventDefault();

    // Set the cookie
    setCookie("wordpress_cookiepref", "all", 365);

    // Close the bar & modal
    $cookieBar.hide();
    closeModal(); // Not needed but lets do it anyway

    location.reload(); // Reload page - will need to come back to this maybe
  });

  // If user customises the cookie consent, we need to know which one (or both) they turned 'OFF'
  // By default we need to turn cookies 'OFF'
  $cookieCustom.on("click touch", function (e) {
    e.preventDefault();

    if ($gaSwitch.prop("checked") && $tpSwitch.prop("checked")) {
      setCookie("wordpress_cookiepref", "all", 365);
    } else if ($gaSwitch.prop("checked") && !$tpSwitch.prop("checked")) {
      setCookie("wordpress_cookiepref", "ga", 365);
    } else if ($tpSwitch.prop("checked") && !$gaSwitch.prop("checked")) {
      setCookie("wordpress_cookiepref", "tp", 365);
    } else {
      setCookie("wordpress_cookiepref", "none", 365);
    }

    // close the bar/modal
    $cookieBar.hide();
    closeModal();

    location.reload(); // Reload page - will need to come back to this maybe
  });

  // Reject all
  // If the user rejects all cookies
  $cookieReject.on("click touch", function (e) {
    e.preventDefault();

    setCookie("wordpress_cookiepref", "none", 365);

    // close the bar/modal
    $cookieBar.hide();
    closeModal();

    location.reload(); // Reload page - will need to come back to this maybe
  });

  // Set a function that can be read site-side
  // Returns boolean
  // function _cookieType($type) {
  //   if( $type == $getPermission || $getPermission == 'all') {

  //     return true;
  //   }

  //   return false
  // }

  // // This puts the function in the JS
  // console.log(_cookieType());

  // Get any inline scripts and reload them so
  //   $(".cookie-reload").each(function () {
  //     var tmp = $(this).html();
  //     $(this).html('');
  //     $(this).html(tmp);
  // });
})();

export default allowCookies;
