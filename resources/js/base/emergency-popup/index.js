import { exists } from "../../utils/helpers";
import { createModal, closeModal } from "../../utils/functions";

const emergencyPopup = (function emergencyPopup() {
  var $emergencyPopup = $("body").find("#emergency-popup");
  var $epseenyet = localStorage.getItem("emergencypopup");
  var $overlay = $("body").find("#overlay");

  if (exists($emergencyPopup)) {
    if ($epseenyet === null) {
      let $content = $emergencyPopup.html();

      createModal($content, "emergency");

      // Show overlay
      $("body").find($overlay).addClass("active");

      // Scroll to modal when opened
      $("html, body").animate(
        {
          scrollTop: $("body").offset().top - 50,
        },
        500
      );

      let $close = $("body").find(".emergency").find(".modal-close");

      // On close, set a session to not show again
      $close.on("click", function (e) {
        e.preventDefault();

        localStorage.setItem("emergencypopup", true);

        closeModal();
      });
    } else {
      $emergencyPopup.hide();
      $("body").find($overlay).removeClass("active");
      closeModal();
    }
  }
})();

export default emergencyPopup;
