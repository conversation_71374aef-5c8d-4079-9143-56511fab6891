import { exists } from "../../utils/helpers";

const mobileMenu = (function mobileMenu() {
  var $mobileMenuTriger = $("#mobile-menu-trigger");
  var $mobilePanel = $("#mobile-menu-panel");

  if (exists($mobileMenuTriger)) {
    $mobileMenuTriger.on("click touch", function () {
      $(this).toggleClass("menu-open");
      $mobilePanel.fadeToggle("fast");
    });
  }

  // Close menu if window is widened
  $(window)
    .on("load resize", function () {
      var $scrollWidth = 960; // should match nav-breakpoint

      if ($(window).width() > $scrollWidth) {
        $mobileMenuTriger.removeClass("menu-open");
        $mobilePanel.fadeOut("fast");
      }
    })
    .resize();

  // Menu items and sub menus
  var $hasSubMenu = $("#menu-mobile-menu").find(".menu-item-has-children");

  $hasSubMenu.prepend(
    '<span class="sub-menu-trigger"><span class="sub-menu-trigger-icon">+</span></span>'
  );

  // Show/hide the submenu on click
  $hasSubMenu.on("click touch", function () {
    console.log("clicked");
    $(this).find(".sub-menu").slideToggle("fast", "linear");
    $(this).toggleClass("sub-menu-open");
  });
})();

export default mobileMenu;
