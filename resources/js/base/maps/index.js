import { exists } from "../../utils/helpers";
import { RGBToHex } from "../../utils/functions";

const maps = (function maps() {
  const $map = document.getElementById("map");

  if (exists($map)) {
    // Grab the primary colour for the map colours
    const $fill = RGBToHex($("body").find(".map").css("fill"));

    // See if theres more than one office on the page
    var $mapMeta = $("body").find(".map-meta");

    // Create a new empty array for our locations
    var locations = new Array();

    // Loop through office to grab their lat/lon
    // Push the array to the locations var
    $mapMeta.each(function (i) {
      let $officename = $(this).find(".office-name").text();
      let $officeaddress = $(this).find(".office-address").text();
      let $lat = $(this).find(".lat").text();
      let $lon = $(this).find(".lon").text();

      // Also, lets build the infowindow
      var content =
        '<div id="map-meta-content">' +
        '<p class="map-meta-title">' +
        $officename +
        "</p>" +
        '<ul class="map-meta-popup">' +
        "<li><p>" +
        $officeaddress +
        "</p></li>" +
        '<li><a href="https://maps.google.co.uk?q=' +
        $officeaddress +
        '" target="_blank" rel="noreferrer">View on Google</a></li>' +
        '<li><a href="https://maps.google.co.uk?q=current location to' +
        $officeaddress +
        '" target="_blank" rel="noreferrer">Get Directions</a></li>' +
        "</ul>" +
        "</div>";

      // Push it to array
      locations.push([content, $lat, $lon, i++]);
    });

    //
    // Make the map
    var map = new google.maps.Map($map, {
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      featureType: "poi",
      styles: [
        {
          featureType: "poi",
          stylers: [{ visibility: "off" }],
        },
        {
          stylers: [{ hue: $fill }, { saturation: -20 }],
        },
      ],
    });

    var bounds = new google.maps.LatLngBounds();
    var infowindow = new google.maps.InfoWindow();

    var marker, i;

    for (i = 0; i < locations.length; i++) {
      marker = new google.maps.Marker({
        position: new google.maps.LatLng(locations[i][1], locations[i][2]),
        map,
      });

      // Extend the bounds to include all markers positions
      bounds.extend(marker.position);

      google.maps.event.addListener(
        marker,
        "click",
        (function (marker, i) {
          return function () {
            infowindow.setContent(locations[i][0]);
            infowindow.open(map, marker);
          };
        })(marker, i)
      );
    }

    // Make the map fit the new bounds
    if ($mapMeta.length > 1) {
      map.fitBounds(bounds);
    } else {
      map.setZoom(17);
      map.setCenter(new google.maps.LatLng(locations[0][1], locations[0][2]));
    }
  } // endif
})();

export default maps;
