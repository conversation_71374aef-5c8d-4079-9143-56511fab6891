import { exists } from "../../utils/helpers";

const searchBar = (function searchBar() {
  var $searchTrigger = $("#site-search");
  var $searchBar = $("#search-bar");
  var $overlay = $("body").find("#overlay");

  $searchTrigger.on("click touch", function () {
    $searchBar.toggleClass("active");
    $searchTrigger.toggleClass("active");

    // Show overlay
    if (exists($overlay)) {
      $overlay.toggleClass("active");
      $overlay.toggleClass("search-overlay");
    }
  });

  // If user clicks off search bar, we want to close it
  $(document).on("click", "#overlay", function () {
    if ($searchBar.hasClass("active")) {
      $searchBar.removeClass("active");
      $searchTrigger.removeClass("active");
      $overlay.removeClass("active");
      $overlay.removeClass("search-overlay");
    }
  });
})();

export default searchBar;
