import { exists } from "../../utils/helpers";

const share = (function share() {
  var $share = $("body").find("#share-button");

  if (exists($share)) {
    $share.on("click touch", function () {
      var $shareContent = $("body").find(".share-link-content");

      // Copy link to clipboard button
      var $copy_button = $shareContent.find(".share-copy");

      $copy_button.on("click touch", function () {
        var $temp = $("<input>");
        var $url = $(location).attr("href");

        $(this).append($temp);
        $temp.val($url).select();
        document.execCommand("copy");
        $temp.remove();

        console.log("Link copied");

        // Give copied feedback
        var $originalText = $(this).text();

        $(this).text("Copied!");
        setTimeout(function () {
          $(this).text($originalText);
        }, 1000);
      });
    });
  }
})();

export default share;
