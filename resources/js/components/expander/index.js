import { exists } from "../../utils/helpers";

const expander = (function expander() {
  var $expander = $(".main-content").find(".expander");

  if (exists($expander)) {
    var $accordTrigger = $expander.find(".expander-trigger"),
      $accordContent = $expander.find(".expander-content");

    $accordTrigger.each(function () {
      var $trigger = $(this);
      var $triggerId = $trigger.attr("data-expander");

      $trigger.on("click", function (e) {
        e.preventDefault();

        if (!$trigger.hasClass("active")) {
          // match the data-attr with the trigger content and show it by adding active
          $accordContent.closest("#" + $triggerId).slideToggle("fast");
          $trigger.addClass("active");
        } else {
          $accordContent.closest("#" + $triggerId).slideUp("fast");
          $trigger.removeClass("active");
        }
      });
    });
  }
})();

export default expander;
