import { exists } from "./helpers";

const setCookie = function setCookie(cname, cvalue, exdays) {
  var d = new Date();
  d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
  var expires = "expires=" + d.toUTCString();
  document.cookie =
    escape(cname) + "=" + escape(cvalue) + ";" + expires + ";path=/;secure;";
};

const getCookie = function getCookie(cname) {
  var name = unescape(cname) + "=";
  var ca = document.cookie.split(";");
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) == " ") {
      c = c.substring(1);
    }
    if (c.indexOf(name) == 0) {
      return c.substring(name.length, c.length);
    }
  }
  return "";
};

const RGBToHex = function RGBToHex(rgb) {
  let sep = rgb.indexOf(",") > -1 ? "," : " ";
  rgb = rgb.substr(4).split(")")[0].split(sep);
  let r = (+rgb[0]).toString(16),
    g = (+rgb[1]).toString(16),
    b = (+rgb[2]).toString(16);
  if (r.length == 1) r = "0" + r;
  if (g.length == 1) g = "0" + g;
  if (b.length == 1) b = "0" + b;

  return "#" + r + g + b;
};

const createModal = function createModal(content, arg) {
  var $modal = `
    <div class="modal ${arg}" id="modal" aria-modal="true">
      <div class="modal-inner">
        <button class="modal-close" id="modal-close" aria-label="Close modal">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <div class="modal-content">
          ${content}
        </div>
      </div>
    </div>
  `;

  // Move popup to the DOM
  $("body").append($modal);

  $("body").find("#modal").addClass("active");
};

const closeModal = function closeModal() {
  var $modal = $("body").find("#modal");

  if (exists($modal)) {
    var $overlay = $("body").find("#overlay");

    $overlay.removeClass("active");
    $modal.removeClass("active");
    $modal.remove();
  }
};

export { setCookie, getCookie, RGBToHex, createModal, closeModal };
