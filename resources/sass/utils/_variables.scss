// Colours
$color-primary: #353E55;
$color-primary-light: lighten($color-primary, 5%);
$color-primary-dark: darken($color-primary, 5%);

$color-secondary: #298B71;
$color-secondary-light: lighten($color-secondary, 5%);
$color-secondary-dark: darken($color-secondary, 5%);

$color-tertiary: #D9BB58;
$color-tertiary-light: lighten($color-tertiary, 5%);
$color-tertiary-dark: darken($color-tertiary, 5%);

$website-bg: #F4F4F4;
$color-transparent: rgba(0,0,0,0);

// Tones
$color-lightgrey: #EEEEEE;
$color-midgrey: #BBBBBB;
$color-darkgrey: #7F8384;

// Base
$color-black: #333333;
$color-white: #FFFFFF;
$color-text: #333333;
$color-blue: #2196F3;
$color-green: #27CF56;
$color-red: #DE4444;
$color-orange: #FD704A;
$color-grey: #848484;
$color-yellow: #FFDC00;

// Spacing
$block-padding: 1rem;
$block-margin: 1rem;
$site-padding: 1rem;
$site-margin: 1rem;

// Borders
$border-size: 1px;
$border-style: solid;
$border-color: $color-black;
$border: $border-size $border-style $border-color;

$border-radius-small: 5px;
$border-radius: 10px;
$border-radius-large: 20px;

// Site
$max-width: 1400px;

// Admin bar
$admin-bar: 32px;

// Mobile admin bar
$mobile-admin-bar: 46px;

// Header height
$header-height: 80px;

// Mobile header height
$mobile-header-height: 80px;

// CTA height
$cta-height: 50px;

// Player aspect ration
$player: calc(100% / 16) * 9;

// Transitions
// Animation/transitions
$transition-time: 0.2s;
$transition: all $transition-time ease-in-out;
$transition-linear: all $transition-time linear .0s;

// Media queries
$mobileXL: "only screen and (min-width: 548px)";
$tablet: "only screen and (min-width: 768px)";
$tabletXL: "only screen and (min-width: 992px)";
$laptop: "only screen and (min-width: 1024px)";
$laptopXL: "only screen and (min-width: 1200px)";
$desktop: "only screen and (min-width: 1400px)";
$maxwidth: "only screen and (min-width: #{$max-width})";

$nav-breakpoint: $tabletXL; // make sure you change in the nav/index.js