@mixin animation($animation) {
  transition: all 0.15s ease-out;
  animation: $animation 0.15s linear;
  animation-iteration-count: 0.5s;
}

@mixin grid-min-max-cols($min-cols, $max-cols, $cols-min-width, $grid-row-gap: 0px, $grid-column-gap: 0px) {
  --min-cols: #{$min-cols};
  --max-cols: #{$max-cols};
  --cols-min-width: #{$cols-min-width};
  --grid-row-gap: #{$grid-row-gap};
  --grid-column-gap: #{$grid-column-gap};

  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min((100%/var(--min-cols) - var(--grid-column-gap)*(var(--min-cols) - 1)/var(--min-cols)), max(var(--cols-min-width), (100%/var(--max-cols) - var(--grid-column-gap)*(var(--max-cols) - 1)/var(--max-cols)))), 1fr));
  gap: $grid-row-gap $grid-column-gap;
}

// Flexbox rows - keeps fixed width
@mixin flexbox-cols-fixed($min-cols, $max-cols, $cols-min-width, $row-gap: 0px, $col-gap: 0px) {
  --min-cols: #{$min-cols};
  --max-cols: #{$max-cols};
  --cols-min-width: #{$cols-min-width};
  --row-gap: #{$row-gap};
  --col-gap: #{$col-gap};

  display: flex;
  flex-wrap: wrap;
  gap: var(--row-gap) var(--col-gap);
  > * {
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: min((100%/var(--min-cols) - var(--col-gap)*(var(--min-cols) - 1)/var(--min-cols)), max(var(--cols-min-width), (100%/var(--max-cols) - var(--col-gap)*(var(--max-cols) - 1)/var(--max-cols))));
  }
}

// Flexbox rows - fill width
@mixin flexbox-cols-adjusted($min-cols, $max-cols, $cols-min-width, $row-gap: 0px, $col-gap: 0px) {
  --min-cols: #{$min-cols};
  --max-cols: #{$max-cols};
  --cols-min-width: #{$cols-min-width};
  --row-gap: #{$row-gap};
  --col-gap: #{$col-gap};

  display: flex;
  flex-wrap: wrap;
  gap: var(--row-gap) var(--col-gap);
  > * {
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: min((100%/var(--min-cols) - var(--col-gap)*(var(--min-cols) - 1)/var(--min-cols)), max(var(--cols-min-width), (100%/var(--max-cols) - var(--col-gap)*(var(--max-cols) - 1)/var(--max-cols))));
  }
}