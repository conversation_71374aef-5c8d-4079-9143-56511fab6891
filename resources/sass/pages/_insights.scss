@use '../utils/variables' as v;
@use '../utils/extends';
@use '../utils/mixins' as *;
@use '../base/typography' as f;

.page-type-insights {
    .insights-search-filters {
        margin: v.$block-margin 0;

        .searchandfilter {
            background-color: v.$color-white;
            padding: v.$block-padding * 2;
            border-radius: v.$border-radius-large;

            ul {
                @extend %reset-liststyle;
                @include grid-min-max-cols(1, 3, 250px, 1rem, 1rem);

                li {
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-end;

                    h4 {
                        margin-bottom: 0;
                        font-weight: f.$font-bold;
                        font-size: f.$h5;
                    }

                    &.sf-field-reset {
                        h4 {
                            display: none;
                        }
                    }
                }
            }
        }
    }

    .insights-search-results-list {
        display: flex;
        flex-flow: row wrap;
        justify-content: flex-start;
        gap: v.$block-margin;
        margin: (v.$block-margin * 1.5) 0;
    }
}