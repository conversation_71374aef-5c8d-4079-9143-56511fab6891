@use '../base/typography' as f;
@use '../utils/variables' as v;

.page-type-home {
    .banner {
        position: relative;
        flex-direction: column;
        align-items: stretch;
        min-height: initial;
        max-height: initial;
        background-color: v.$color-white;
        color: v.$color-primary;
        
        @media #{v.$tablet} {
            flex-direction: row;
        }

        .banner-text {
            
            @media #{v.$tablet} {
                flex-basis: 50%;
            }

            .container {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;

                .hero-content {
                    padding: v.$block-padding;
                    font-size: f.$h1;
                    line-height: 1.3;

                    @media #{v.$tablet} {
                        font-size: f.$h1;
                        max-width: 620px;
                    }

                    @media #{v.$nav-breakpoint} {
                        margin-top: v.$cta-height;
                        font-size: f.$h1 + 0.5;
                    }

                    span {
                        white-space: nowrap;
                        font-weight: f.$font-bold;
                        color: v.$color-secondary;

                        @media #{v.$nav-breakpoint} {
                            display: block;
                        }

                        &.tertiary {
                            color: v.$color-tertiary;
                        }
                    }

                    .button-tertiary {
                        // display: inline-block;
                    }
                }
            }
        }

        .banner-image {
            flex-basis: 50%;

            img {
                height: 100%;
                max-height: 400px;

                @media #{v.$nav-breakpoint} {
                    min-height: 400px;
                    max-height: initial;
                }
            }
        }

    }

    .main-article {
        text-align: center;
        padding: calc(v.$block-padding * 2.5);
    }
}