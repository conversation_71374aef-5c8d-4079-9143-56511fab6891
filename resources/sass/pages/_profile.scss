@use '../utils/variables' as v;
@use '../base/typography' as f;

.page-type-profile {
    .banner {
        position: relative;
        display: flex;
        flex-direction: column;
        max-height: initial;

        @media #{v.$tablet} {
            // flex-direction: row;
            // max-height: 30em;
        }

        img {
            width: 100%;
            max-height: 350px;

            @media #{v.$tablet} {
                height: 600px;
                max-height: initial;
                object-position: 50% 10%;
            }
        }

        .container {
            display: flex;
            align-items: flex-end;
            
            @media #{v.$tablet} {
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                height: 100%;
                padding: v.$block-padding;
            }
        }

        

        .profile-banner-content {
            display: flex;
            flex-direction: column;
            background-color: v.$color-primary;
            padding: v.$block-padding;
            color: v.$color-white;
            width: 100%;
            
            @media #{v.$tablet} {
                width: 400px;
                padding: v.$block-padding (v.$block-padding * 2);
                border-radius: v.$border-radius-large;
                margin-bottom: v.$block-margin;
            }

            @media #{v.$laptop} {
                width: 500px;
            }

            a {
                display: flex;
                align-items: center;
                color: v.$color-white;
                margin-bottom: calc(v.$block-margin / 2);
                text-decoration: none;

                &:hover{
                    text-decoration: underline;
                }

                .icon {
                    width: 20px;
                    max-height: 20px;
                    margin-right: v.$block-margin;
                    fill: v.$color-white;
                }
            }

            h1 {
                margin-bottom: 0;
            }

            .job-title {
                margin-bottom: v.$block-margin;
            }

            .social-icons {
                .social-link {
                    display: flex;
                    justify-content: center;
                    background-color: v.$color-white;
                    border-radius: v.$border-radius;
                    width: 30px;
                    height: 30px;

                    .icon {
                        fill: v.$color-primary;
                        margin-right: 0;
                    }
                }
            }
        }
    }

    .sidebar {
        .specialisms,
        .offices {
            background-color: v.$color-white;
            border-radius: v.$border-radius-large;
            padding: v.$block-padding * 2;
            margin-bottom: v.$block-margin;

            a {
                display: block;
            }
        }
    }
}