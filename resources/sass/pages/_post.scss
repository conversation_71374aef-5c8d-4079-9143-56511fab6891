@use '../utils/variables' as v;
@use '../base/typography' as f;


.post-type-post {
    .post-details {
        background-color: v.$color-white;
        border-radius: v.$border-radius-large;
        padding: v.$block-padding * 2;
        margin-bottom: v.$block-margin;

        .posted {
            margin-bottom: v.$block-margin;
        }

        .categories {
            margin-bottom: v.$block-margin;
        }
    }

    .author {
        background-color: v.$color-white;
        border-radius: v.$border-radius-large;
        padding: v.$block-padding * 2;
        margin-bottom: v.$block-margin; 
    }

    h1 {
        font-size: f.$h1 - 0.6;

        @media #{v.$nav-breakpoint} {
            font-size: f.$h1;
        }
    }
}