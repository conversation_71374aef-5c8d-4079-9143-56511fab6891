@use '../utils/variables' as v;
@use '../base/typography' as f;
@use '../utils/extends';

.post-name-search {
    .main-article {
        .searchandfilter {
            margin-bottom: v.$block-margin;
    
            ul {
                display: flex;
                gap: v.$block-padding;
                flex-flow: column wrap;
                align-items: center;
                @extend %reset-liststyle;            
    
                @media #{v.$tablet} {
                    flex-flow: row wrap;
                }
    
                li {
                    width: 100%;
    
                    @media #{v.$tablet} {
                        width: auto;
                        flex-grow: 1;
                    }
                }
            }
    
            .sf-input-text {
                max-width: initial;
            }
    
            .sf-field-search {
                flex-basis: 75%;
            }
    
            .sf-field-reset,
            .sf-field-submit {
                h4 {
                    display: none;
                }
    
                align-self: flex-end;
    
                input {
                    margin: 0;
                    max-width: initial;
                }
            }
        }
    }

    .search-results-list {
        display: flex;
        flex-direction: column;
        gap: v.$block-margin;
        padding: v.$block-padding 0;
    }
}

.results-count {
    span {
        font-weight: f.$font-bold;
    }
}

.search-result-card {
    display: flex;
    background-color: v.$color-white;
    border-radius: v.$border-radius-large;
    padding: v.$block-padding;

    @media #{v.$tablet} {
        padding: v.$block-padding * 2;
    }

    .card-image {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            background-color: v.$color-lightgrey;
            border-radius: v.$border-radius;
            width: 100%;
            max-width: 100%;
            max-height: 350px;
            object-fit: contain;
            object-position: center;

            @media #{v.$tablet} {
                width: initial;
                max-width: 350px;
                max-height: 350px;
            }
        }
    }

   

    &.search-result-post {
        gap: v.$block-padding;
        flex-direction: column;

        @media #{v.$tablet} {
            flex-direction: row;
        }

        .card-content-wrapper {
            .card-author-date {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
        }
    }
    
    &.search-result-profile {
        gap: v.$block-padding;
        flex-direction: column;

        @media #{v.$tablet} {
            flex-direction: row;
        }

        .card-content {

        }
    }
    
    &.search-result-page {
      flex-direction: column;

        .result-header {
            margin-bottom: v.$block-margin;

            .result-title {
                font-size: f.$h3;
            }
        }

        .result-synopsis {
            margin-bottom: v.$block-margin;
        }
    }
}

