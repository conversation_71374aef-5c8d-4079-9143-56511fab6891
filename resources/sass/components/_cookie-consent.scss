@use '../utils/variables' as v;
@use '../base/typography' as f;

.cookie-consent {
    display: none;

    &.active {
        display: block;
    }
}

.modal-inner {    
    .h1 {
        font-size: f.$h2;
    }

    .h3 {
        font-weight: f.$font-bold;
    }

    .cookie-intro {
        margin-bottom: v.$block-margin;
    }

    .cookie-content-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .cookie-type {
            display: flex;
            gap: 0.5rem;
            flex-direction: column;
            align-items: flex-end;

            @media #{v.$mobileXL} {
                flex-direction: row;
                align-items: center;
            }
        }
    }

    .cookie-switch {
        display: flex;
        align-items: center;

    }

    .cookie-consent-footer {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: flex-end;
        padding: v.$block-padding 0;

        button {
            width: 100%;

            @media #{v.$mobileXL} {
                width: auto;
            }
        }

    }
}