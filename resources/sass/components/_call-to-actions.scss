@use '../utils/variables' as v;
@use '../utils/functions' as z;

.call-to-actions {
    display: none;
    
    @media #{v.$nav-breakpoint} {
        display: block;
        position: absolute;
        top: v.$header-height;
        left: 0;
        right: 0;
        width: 100%;
        background-color: rgba(v.$color-primary, 0.3);
        // height: v.$cta-height;
        z-index: z.z(nav) - 1;
    }

    .container {
        display: flex;
        justify-content: flex-end;
        flex-direction: row;
        gap: v.$site-padding;
        height: 100%;
        align-items: center;
        padding: calc(v.$site-padding / 2) v.$site-padding;
    }
}

.mobile-menu-panel {
    .call-to-actions {
        display: block;
        position: relative;

        .container {
            justify-content: center;
            flex-direction: column;

            a {
                width: 100%;
            }
        }
    }
}