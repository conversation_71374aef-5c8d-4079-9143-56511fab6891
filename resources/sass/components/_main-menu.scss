@use '../utils/variables' as v;
@use '../utils/extends';

#main-menu {
    display: none;

    @media #{v.$nav-breakpoint} {
        display: flex;
    }

    #menu-main-menu {
        @extend %reset-liststyle;
        display: flex;
        align-items: center;
        height: 100%;

        li {
            height: 100%;
        }

        .menu-item {
            position: relative;
            display: flex;
            align-items: center;
            padding: 0 calc(v.$block-padding / 2);
            transition: v.$transition;

            a {
                display: flex;
                align-items: center;
                @extend %white-link;
                text-decoration: none;
                height: 100%;
            }            

            &.menu-item-has-children {
                >a {
                    position: relative;

                    &:after {
                        content: '';
                        display: inline-block;
                        background-color: v.$color-white;
                        mask-image: url('../images/icons/icon-chevron-down.svg');
                        height: 16px;
                        width: 13px;
                        margin-left: 3px;
                        transition: v.$transition;
                    }

                    // Change the arrow colour too
                    &:hover,
                    &:active,
                    &:focus {
                        &:after {
                            background-color: v.$color-secondary;
                        }
                    }
                }
                
                .sub-menu {
                    @extend %reset-liststyle;
                    position: absolute;
                    top: -100rem;
                    width: 275px;
                    transform: translateX(-#{v.$header-height});
                    background-color: v.$color-secondary;

                    .menu-item {
                        a {
                            width: 100%;
                            padding: calc(v.$block-padding / 2) v.$block-padding;
                        }
                        
                        &:hover,
                        &:active,
                        &:focus {
                            background-color: v.$color-primary-dark;
                        }
                    }
                }

                // Show the sub-nav
                &:hover,
                &:active,
                &:focus {
                    .sub-menu {
                        top: v.$header-height;
                        transform: translateX(0);
                    }
                }
            }
        }

         // Parent links only
         >.menu-item {
            &:first-child {
                padding-left: 0;
            }
            &:last-child {
                padding-right: 0;

                .sub-menu {
                    right: 0;
                }
            }

            // &.current-menu-ancestor,
            // &.current-menu-item {
            //     >a {
            //         color: v.$color-secondary;
            //     }
            // }

            >a {
                &:hover,
                &:active,
                &:focus {
                    color: v.$color-secondary;
                }
            }
        }
    }
}