@use '../utils/variables' as v;
@use '../base/typography' as f;
@use '../utils/extends';

.map {
    display: block;
    position: relative;
    width: 100%;
    height: 400px;
    fill: v.$color-primary;
    user-select: none;
    border-radius: v.$border-radius;
    margin-bottom: v.$block-margin;
  }
  
  #map-meta-content {
    padding: v.$block-padding;
    
    .map-meta-title {
      font-size: f.$h5;
      font-weight: f.$font-bold;
    }
    
    .map-meta-popup {
      @extend %reset-liststyle;
      font-size: f.$font-size-body - 0.1;
      margin: 0;
    
      p, a {
        font-weight: f.$font-normal;
      }
    }
  }
  