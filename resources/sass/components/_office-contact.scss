@use '../utils/variables' as v;
@use '../base/typography' as f;
@use '../utils/extends';
@use '../utils/mixins' as *;


.office-list {
    @include grid-min-max-cols(1, 3, 250px, 10px, 10px);
    padding: v.$block-padding;
    border-bottom: 1px solid v.$color-midgrey;

    &:last-of-type {
        border-bottom: none;
    }

    .office-details {
        flex-basis: 50%;
    }

    .office-address {
        span {
            display: block;
        }
    }

    .office-contact {
        .phone-number {
            &::before {
                content: 'Tel: ';
            }
        }
        .faxnumber {
            &::before {
                content: 'Fax: ';
            }
        }
        .office-email {
            &::before {
                content: 'E: ';
            }
        }
    }
}