@use '../utils/variables' as v;
@use '../utils/extends';
@use '../utils/mixins' as *;
@use '../base/typography' as f;

.service-links {
    background-color: v.$color-primary;

    .container {
        nav {     
            ul {
                @extend %reset-liststyle;
                @include grid-min-max-cols(1, 5, 250px);
                
                li {
                    flex: 1;
                    display: flex;
                    align-items: stretch;
                    justify-content: center;
                    min-height: 150px;
                    min-width: 50%;

                    @media #{v.$tablet} {
                        min-width: 150px;
                    }

                    a {
                        flex: 1;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        @extend %white-link;
                        text-decoration: none;
                        padding: calc(v.$block-padding * 2) v.$block-padding;
                        font-size: f.$font-size-body + 0.2;
                        font-weight: f.$font-light;

                        @media #{v.$tablet} {
                            font-size: f.$h3;
                        }

                        &:hover {
                            background-color: v.$color-secondary;
                        }
                    }
                }
            }
        }
    }
}