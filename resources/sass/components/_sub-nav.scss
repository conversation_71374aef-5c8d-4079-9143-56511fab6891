@use '../utils/variables' as v;
@use '../utils/extends';

.sub-nav {
    margin-bottom: v.$block-margin;
    border-radius: v.$border-radius-large;
    background-color: v.$color-white;
    padding: v.$block-padding * 2;

    .title {
        a {
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    ul {
        @extend %reset-liststyle;

        li {
            line-height: 1.3rem;
            margin-bottom: calc(v.$block-margin / 2);

            a {
                text-decoration: none;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}