@use '../utils/variables' as v;
@use '../utils/mixins' as *;
@use '../base/typography' as f;


.share-link-container {
    display: none;
}

.share-link-content {
    .share-links-grid {
        @include grid-min-max-cols(1, 2, 250px, 10px, 10px);

        a,
        button {
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            align-items: center;
            background: none;
            height: 75px;
            border: 2px solid #e7eaf3;
            border-radius: v.$border-radius;
            padding: v.$block-padding;
            color: v.$color-text;
            font-size: f.$h4;
            text-align: left;
            text-decoration: none;
            transition: v.$transition;
            line-height: 1.5rem;

            .icon {
                width: 20px;
                max-height: 20px;
                margin: 0;
                margin-right: 10px;
                fill: v.$color-text;
            }

            &:hover {
                .icon {
                    fill: v.$color-white;
                }
            }

            &.share-twitter {
                .icon {
                    fill: #1DA1F2;
                }
            }
            &.share-linkedin {
                .icon {
                    fill: #0e76a8;
                }
            }
            &.share-whatsapp {
                .icon {
                    fill: #25D366;
                }
            }
            &.share-facebook {
                .icon {
                    fill: #3b5998;
                }
            }

            &:hover {
                text-decoration: none;
                border-color: v.$color-primary;
                background-color: v.$color-primary;
                fill: v.$color-white;
                color: v.$color-white;

                .icon {
                    fill: v.$color-white;
                }
            }
        }
        
        .share-url {
            height: 100%;
            margin-bottom: 0;

            &:hover {
                pointer-events: none;
            }
        }

        #copied {
            display: none;
        }   
    }

    .qr-code-container {
        display: flex;
        flex-direction: column;
        align-content: center;
        justify-content: center;
        padding: v.$block-padding 0;
        text-align: center;

        .qr-code {
            max-width: 200px;
            margin: 0 auto;
            padding: v.$block-padding;
        }
    }
}