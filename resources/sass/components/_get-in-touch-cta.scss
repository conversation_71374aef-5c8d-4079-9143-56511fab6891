@use '../utils/variables' as v;

.get-in-touch-cta {
    height: 300px;
    background-color: v.$color-primary;

    @media #{v.$tablet} {
        height: 450px;
    }

    .container {
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: center;
        padding: calc(v.$block-padding * 2) v.$block-padding;

        .git-cta-rotator {
            margin-bottom: 20px;

            &.swiper {
                width: 100%;
                // height: 250px;
                min-height: 100px;
                margin-left: 0;

                @media #{v.$tablet} {
                    width: 45%;
                    // min-height: 100px;
                    height: auto;
                }
              }
          
              .swiper-slide {
              }

            .swiper-pagination {
                text-align: left;
                bottom: -7px;
            }

            .swiper-pagination-bullet {
                background: v.$color-grey;

                &.swiper-pagination-bullet-active {
                    background: v.$color-secondary;
                }
            }
        }

        h2 {
            color: v.$color-white;
        }

        .button-secondary {
            &:hover {
                background-color: v.$color-secondary-dark;
            }
        }

        &:after {
            content: '';
            display: none;
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            width: 150px;
            background-image: url('../images/m.svg');
            background-position: bottom right;
            background-repeat: no-repeat;
            background-size: 150px;

            @media #{v.$mobileXL} {
                display: block;
            }
            
            @media #{v.$tablet} {
                background-position: center;
                width: 500px;
                background-size: calc(100% - #{v.$block-padding * 5});
            }

            @media #{v.$laptop} {
                width: 684px;
            }
        }
    }
}