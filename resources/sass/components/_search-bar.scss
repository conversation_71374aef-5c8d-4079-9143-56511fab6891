@use '../utils/variables' as v;
@use '../utils/extends';
@use '../utils/functions' as z;
@use '../blocks/inputs/button';

.search-bar {
    top: -250px;
    position: fixed;
    left: 0;
    right: 0;
    z-index: z.z(nav) - 1;
    background-color: v.$color-primary;
    padding: calc(v.$block-padding / 2);
    transition: top 300ms cubic-bezier(0.17, 0.04, 0.03, 0.94);

    &.active {
        top: v.$header-height;
    }

    .searchandfilter {
        flex-direction: row;
        background-color: initial;
        border-radius: 0;
        padding: 0;
        margin: 0 auto;

        ul {
            @extend %reset-liststyle;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: center;
            width: 100%;

            li {
                display: inline-block;
            }
        }

        h4 {
            display: none;
        }

        .sf-field-search {
            flex-grow: 1;
            max-width: 800px;
        }

        .sf-input-text {
            width: 100%;
            max-width: none;
        }

        .sf-field-submit {
            input {
                @extend .button-secondary;
                height: 58px;
                margin: 0;
                margin-left: 5px;

                &:hover {
                    background-color: v.$color-secondary-dark;
                }
            }
        }
    }
}