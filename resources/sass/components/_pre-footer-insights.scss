@use '../utils/variables' as v;
@use '../utils/mixins' as *;

.pre-footer-insights {
    margin-top: calc(v.$block-margin * 2);

    .container {
        padding: v.$block-padding;

        h2 {
            text-align: center;
            width: 100%;
        }

        .insights-wrapper {
            @include grid-min-max-cols(1, 3, 250px, 1rem, 1rem);

            .insight-result-card {
                max-width: 100%;
        
                @media #{v.$tablet} {
                    max-width: 100%;
                }
                @media #{v.$laptop} {
                    max-width: 100%;
                }
            }
        }
    }
}