@use '../utils/variables' as v;
@use '../utils/functions' as z;
@use '../utils/extends';

.cookie-bar {
    position: fixed;
    // display: none;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: v.$color-primary;
    color: v.$color-white;
    z-index: z.z(popup);
  
    @media #{v.$nav-breakpoint} {
        height: 200px;
    }

    &.active {
      display: block;
    }

    .container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        height: 100%;
        padding: v.$block-padding;

        @media #{v.$tablet} {
            flex-direction: row;
        }
    }
  
    .cookie-message {
        flex-grow: 1;
        padding-bottom: v.$block-padding;

        @media #{v.$tablet} {
            padding-bottom: 0;
        }

        a {
            @extend %white-link;
        }
    }

    .cookie-actions {
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
        gap: 0.5rem;

        button {
            width: 100%;

            @media #{v.$mobileXL} {
                width: auto;
            }
        }

        .cookie-settings {
            min-width: auto;
            margin: 0 v.$block-margin;
            color: v.$color-white;

            &:hover {
                text-decoration: underline;
            }
        }

        .reject-cookies {
            border-color: v.$color-white;
            color: v.$color-white;

            &:hover {
                background-color: v.$color-white;
                color: v.$color-text;
            }
        }

        .allow-cookies {
            &:hover {
                background-color: v.$color-secondary-dark;
            }
        }
    }
}
  