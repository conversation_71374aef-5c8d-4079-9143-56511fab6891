@use '../utils/variables' as v;
@use '../utils/extends';
@use '../utils/functions' as z;

#mobile-menu-trigger {
    @extend %reset-buttonstyle;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: v.$header-height;
    height: v.$header-height;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;

    .menu-label {
        display: none;
        margin-top: 10px;
        margin-bottom: -15px;
        color: v.$color-white;
        font-size: 0.8rem;
        text-transform: uppercase;
    }

    .bar {
        position: relative;
        width: 30px;
        height: 2px;
        stroke-linecap: round;
        background-color: v.$color-white;
        transition: all 0.3s cubic-bezier(0.4, 0.01, 0.165, 0.99);
        transition-delay: 0s;
        opacity: 1;

        &.top {
            transform: translateY(-6px) rotate(0deg);
        }
        &.bottom {
            transform: translateY(6px) rotate(0deg);
        }
    }

    &.menu-open {
        .bar {
            transition: all 0.4s cubic-bezier(0.4, 0.01, 0.165, 0.99);
            transition-delay: 0.1s;

            &.centre {
                opacity: 0;
            }

            &.top {
                transform: translateY(5px) rotate(45deg);
            }
            &.bottom {
                transform: translateY(1px) rotate(-45deg);
            }
        }
    }

    @media #{v.$nav-breakpoint} {
        display: none;
    }
}

#mobile-menu-panel {
    display: none;
    position: fixed;
    top: v.$mobile-header-height;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100vh;
    background-color: v.$color-primary;
    color: v.$color-white;
    z-index: z.z(nav) + 1;

    &.active {
        display: block;
    }

    .mobile-panel-inner {
        padding: v.$block-padding;
        overflow-y: scroll;
        height: 100%;
    }

    .mobile-menu {
        ul {
            @extend %reset-liststyle;
        }

        .menu {
            .menu-item {
                border-bottom: 1px solid #85898A;
                margin-top: 5px;

                a {
                    width: 100%;
                    line-height: 2.35;
                    text-decoration: none;
                    color: v.$color-white;
                }

                &.menu-item-has-children {
                    -webkit-tap-highlight-color: transparent;

                    .sub-menu-trigger {
                        float: right;
                        line-height: 30px;
                        color: v.$color-white;
                        height: 100%;
                        width: 50px;
                        text-align: right;
                        cursor: pointer;
                        
                        .sub-menu-trigger-icon {
                            transition: v.$transition;
                        }
                    }

                    &.sub-menu-open {
                        .sub-menu-trigger {
                            .sub-menu-trigger-icon {
                                transform: rotate(45deg) scale(1.08);
                            }
                        }
                    }
                }
            }

            .sub-menu {
                display: none;
                margin-left: v.$block-margin;
        
                .menu-item {
                  &:last-child {
                    border-bottom: 0;
                  }
                }
            }
        }
    }
}