@use '../utils/variables' as v;
@use '../base/typography' as f;

.expander {
    position: relative;
    display: block;
    width: 100%;
    // margin: ($site-margin * 2) 0 $site-margin 0;
    list-style: none !important;
    padding-left: 0 !important;
  
    li {
      border-bottom: 1px solid v.$color-primary;
    }
  
    .expander-trigger {
      display: block;
      position: relative;
      margin-bottom: calc(v.$block-margin / 4);
      width: 100%;
      padding: v.$site-padding 0;
      padding-right: 50px;
      cursor: pointer;
  
      h4 {
        display: inline-block;
        // color: $color-text;
        // font-size: $font-size-body + 0.1;
        letter-spacing: initial;
        margin: 0;
        // font-family: $font-family-body;
      }
  
      &:hover {
        text-decoration: none;
      }
  
      &:after {
        content: '+';
        position: absolute;
        top: v.$block-padding;
        right: v.$block-padding;
        transition: v.$transition;
        font-size: 35px;
        // font-weight: $font-light;
        color: v.$color-primary;
      }
  
        &.active {
          &:after {
            transform: rotate(45deg) scale(1.08);
          }
        }
  
    }
  
    .expander-content {
      display: none;
      width: 100%;
      overflow: hidden;
      padding: v.$site-padding;
    //   font-size: $font-size-body;
  
      @media #{v.$tablet} {
        padding: v.$site-padding (v.$site-padding * 1.5);
      }
    }
  }
  