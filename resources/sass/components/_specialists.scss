@use '../utils/variables' as v;
@use '../base/typography' as f;
@use '../utils/extends';

.specialist-team-container {
    margin-bottom: v.$block-margin;
    border-radius: v.$border-radius-large;
    background-color: v.$color-white;
    padding: v.$block-padding * 2;

    .member {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: v.$block-margin;

        &:last-of-type {
            margin-bottom: 0;
        }

        h4 {
            display: flex;
            flex-direction: column;
            font-size: f.$font-size-body;
            font-family: f.$font-family-body;
            margin: 0;
            margin-left: v.$block-margin;
            font-weight: f.$font-bold;

            .span {
                font-weight: f.$font-normal;
                font-family: f.$font-family-body;
                font-size: f.$font-size-body - 0.1;
            }
        }

        img {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            object-fit: cover;
            object-position: center;
        }
    }
}