@use '../utils/variables' as v;
@use '../utils/extends';
@use '../utils/mixins' as *;

.social-icons {
    display: flex;
    gap: v.$block-padding;
    margin-bottom: v.$block-margin;

    .social-link {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background-color: v.$color-white;
        width: 40px;
        height: 40px;

        &:hover {
            background-color: v.$color-secondary;
        }
        
        .icon {
            width: 25px;
            height: 25px;
            fill: v.$color-primary;
        }
    }
}