@use '../utils/variables' as v;
@use '../utils/functions' as z;
@use '../base/typography' as f;

.banner {
    position: relative;
    display: flex;
    align-items: center;
    background-color: v.$color-primary-light;
    color: v.$color-white;
    min-height: 250px;
    z-index: 0;
    max-height: 15rem;
    overflow: hidden;

    @media #{v.$nav-breakpoint} {
        max-height: 30rem;
        min-height: calc(350px + v.$cta-height);
    }

    &.darken {
        &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            background-color: rgba(v.$color-black, 0.4);
            z-index: z.z(front);
        }
    }

    img {
        display: block;
        width: 100%;
        max-width: none;
        min-height: inherit;
        object-position: center;
        object-fit: cover;
    }

    .content-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        z-index: z.z(front) + 1;

        .container {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
            padding: v.$site-padding;       
        }

        h1,
        .h1 {
            font-size: f.$h1 - 0.6;
            background-color: v.$color-tertiary;
            padding: calc(v.$block-padding / 2) v.$block-padding;
            border-radius: 20px;

            @media #{v.$nav-breakpoint} {
                font-size: f.$h1;
            }
        }
    }
}