@use '../utils/variables' as v;
@use '../base/typography' as f;
@use '../utils/mixins' as *;
@use '../utils/extends';

#footer {
    display: flex;
    align-items: center;
    padding: calc(v.$block-padding * 2) v.$block-padding;
    background-color: v.$color-primary;
    color: v.$color-white;
    margin-top: auto;
    font-size: f.$font-size-body - 0.1;

    .container {
        @include grid-min-max-cols(1, 4, 250px);

        .column {
            padding: v.$block-padding;

            &.legal-column {
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .logo {
                    max-width: 175px;
                    margin-bottom: v.$block-margin;
                }

                .legal-text {
                    p {
                        font-size: inherit;
                    }
                }

                .menu {
                    @extend %reset-liststyle;
                    margin-top: v.$block-margin;

                    .menu-item {
                        display: inline;

                        a {
                            @extend %white-link;
                            text-decoration: none;
                            font-size: f.$font-size-body - 0.1;
                            white-space: nowrap;

                            &:hover {
                                color: v.$color-secondary;
                            }
                        }

                        &::after {
                            content: ' | ';
                        }

                        &:last-of-type {
                            &::after {
                                content: none;
                            }
                        }
                    }
                }
            }

            &.office-column {
                span {
                    display: block
                }

                p {
                    font-size: inherit;

                    a {
                        @extend %white-link;
                        text-decoration: none;
                        font-size: f.$font-size-body - 0.1;
                        white-space: nowrap;

                        &:hover {
                            color: v.$color-secondary;
                        }
                    }
                }

                .office-name {
                    @extend %white-link;
                    text-decoration: none;

                    &:hover {
                        color: v.$color-secondary;
                    }
                }

                .office-contact {
                    .phone-number,
                    .office-email {
                        a {
                            @extend %white-link;
                            text-decoration: none;

                            &:hover {
                                color: v.$color-secondary;
                            }
                        }
                    }

                    .office-email {
                        margin: calc(v.$block-margin / 2) 0;
                    }

                    .phone-number {
                        &:before {
                            content: 'Tel: '
                        }
                    }

                    .faxnumber {
                        &:before {
                            content: 'Fax: '
                        }
                    }
                }
            }

            &.company-column {
                .menu {
                    @extend %reset-liststyle;

                    .menu-item {
                        a {
                            @extend %white-link;
                            text-decoration: none;

                            &:hover {
                                color: v.$color-secondary;
                            }
                        }
                    }
                }
            }

            &.social-column {
                .sra-statement {
                    margin-top: v.$block-margin;
                    font-size: f.$font-size-body - 0.2;
                }
            }

            p {
                &.column-header {
                    font-size: f.$font-size-body;
                    color: v.$color-white;
                    margin-bottom: calc(v.$block-margin / 2);
                }
            }
        }
    }

    h4 {
        color: v.$color-secondary;
    }
}