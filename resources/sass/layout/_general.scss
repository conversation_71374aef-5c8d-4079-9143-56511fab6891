@use '../utils/variables' as v;
@use '../base/typography' as f;


.main-content {
    .container {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        gap: 1rem;
        padding: v.$site-padding;

        .main-article {
            flex-grow: 9999;
            flex-basis: 500px;
            padding-bottom: v.$block-padding * 2;

            h1 {
                line-height: 1.2;
                margin-bottom: v.$block-margin * 2;
            }

            h3 {
                margin-top: v.$block-margin * 2;
                margin-bottom: calc(v.$block-margin / 2);
                font-weight: f.$font-bold;
                font-size: f.$h4;
                color: v.$color-primary;
            }

            p {
                margin-bottom: v.$block-margin;
            }

            // ul {
            //     list-style: none;

            //     li {
            //         &:before {
            //             content: "\2022";
            //             color: v.$color-tertiary;
            //             font-weight: bold;
            //             display: inline-block;
            //             width: 1em;
            //             margin-left: -1em;
            //         }
            //     }
            // }
        }
        
        .sidebar {
            flex-basis: 400px;
            flex-grow: 1;
        }
    }
}

