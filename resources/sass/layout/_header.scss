@use '../utils/variables' as v;
@use '../utils/functions' as z;

#main-header {
    flex-shrink: 0;
    height: v.$header-height;
    background-color: v.$color-primary;

    .container {
        display: flex;
        justify-content: space-between;
        padding: 0 v.$block-padding;
        height: 100%;

        .logo {
            display: flex;
            align-items: center;

            img {
                max-width: 130px;

                @media #{v.$nav-breakpoint} {
                    max-width: none;
                    height: calc(#{v.$header-height} - #{v.$block-padding * 1.5});
                }
            }
        }

        .main-menu-wrapper {
            display: flex;
            
            #site-search {
                display: flex;
                align-items: center;
                justify-content: center;
                background: none;
                border: none;
                width: v.$header-height;

                @media #{v.$nav-breakpoint} {
                    width: auto;
                    padding-left: v.$block-padding;
                }

                &:hover {
                    cursor: pointer;
                }
    
                .icon {
                    width: 18px;
                    height: 18px;
                    fill: v.$color-white;
                }

                .cross {
                    display: none;
                }

                &.active {
                    .search {
                        display: none;
                    }
                    .cross {
                        display: block;
                    }
                }
            }
        }
    }
}

.fixed-header {
    #main-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: z.z(nav);
    }

    main {
        margin-top: v.$header-height;
    }
}