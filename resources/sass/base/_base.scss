@use './typography' as f;
@use '../utils/variables' as v;
@use '../utils/functions' as z;
@use '../utils/extends';

*,
*:before,
*:after {
    box-sizing: border-box;
}

html,
body {
    min-height: 100%;
    margin: 0;
}

body {
    position: relative;
    display: flex;
    flex-direction: column;
    scroll-behavior: smooth;
    background-color: v.$website-bg;
    font-family: f.$font-family-body;
    line-height: 1.7;
    height: 100vh;


    &.logged-in {        
        &.fixed-header {
            #main-header {
                margin-top: v.$mobile-admin-bar;
                
                @media #{v.$tablet} {
                    margin-top: v.$admin-bar;
                }
            }
            .search-bar {
                &.active {
                    margin-top: v.$mobile-admin-bar;
                
                    @media #{v.$tablet} {
                        margin-top: v.$admin-bar;
                    }
                }
            }
        }
    }
}

::selection {
    color: v.$color-white;
    background: v.$color-primary-dark;
  }

// Typography
h1,h2,h3,h4,h5,h6 {
    margin-bottom: f.$header-margin;
    font-family: f.$font-family-heading;
    font-weight: f.$font-light;
}

h1 {
    font-size: f.$h1;
}

h2 {
    font-size: f.$h2;
}

h3 {
    font-size: f.$h3;
}

h4 {
    font-size: f.$h4;
}

h5 {
    font-size: f.$h5;
}

h6 {
    font-size: f.$h6;
}

p {
    font-size: f.$font-size-body;
}

strong,b {
    font-weight: f.$font-bold;
}

em {
    font-style: f.$font-italic;
}

del {
    text-decoration: line-through;
}

small {
    font-size: f.$h6;
}

img {
    display: block;
    height: auto;
    max-width: 100%;
}

// Overlay
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: z.z(modal) - 1;
    background-color: rgba(v.$color-primary, 0.7);
    transition: v.$transition;
    opacity: 0;
    pointer-events: none;
  
    &.active {
      opacity: 1;
      pointer-events: auto;
    }

    &.search-overlay {
        z-index: z.z(nav) - 2;
    }
  }

  
// Lists
ul,ol {
    padding-left: v.$block-padding;
    margin-left: v.$block-margin;
}

ul {
    list-style-type: disc;
}

// Links
a {
    color: v.$color-primary;
    text-decoration: underline;
    text-underline-offset: 2px;
    transition: v.$transition;

    &:hover,
    &.focus {
        color: v.$color-secondary-dark;
        text-decoration: underline;
    }
}

a[href^="http"],
.external-link {
    // 
}

// Skip to content
.skip-to-content {
    position: absolute;
    background: v.$color-white;
    height: 60px;
    left: 50%;
    padding: v.$block-padding;
    transform: translateY(-100%);
    transition: transform 0.3s;
    z-index: 999;

    &:focus {
        transform: translateY(0%);
    }
}

// Screen reader text
.sr-only,
.screen-reader-text,
.screen-reader-response {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0,0,0,0);
    white-space: nowrap;
    clip-path: inset(50%);
    border: 0;

    &:focus {
        position: static;
        width: auto;
        height: auto;
        overflow: visible;
        clip: auto;
        white-space: normal;
        clip-path: none;
    }
}

// Containers
.container {
    width: 100%;
    max-width: v.$max-width;
    margin: 0 auto;
}

// Sections
section {
    flex-shrink: 0;
}

// Icons
.icon {
    display: block;
}

// Author card
.author-block {
    display: flex;
    align-items: center;

    .avatar {
        width: 50px;
        height: 50px;
        border-radius: 25px;
        object-fit: cover;
        object-position: center;
        margin-right: calc(v.$block-margin / 2);
    }

    .author-name {
        font-size: f.$h5;
        font-weight: f.$font-bold;

        a {
            text-decoration: none;
        }

        .job-title {
            font-size: f.$h6;
            font-weight: f.$font-light;
        }
    }
}

// Categories
.post-categories {
    display: inline-flex;
    margin: calc(v.$block-margin / 2) 0;

    a {
        padding: calc(v.$block-padding / 2);
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }

        &:first-of-type {
            padding-left: 0;
        }
    }
}

// Tags
.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;

    .tag {
        border-radius: v.$border-radius;
        border: 2px solid v.$color-transparent;
        font-size: f.$font-size-body - 0.1;
        color: #85898A;
        padding: 0 calc(v.$block-padding / 2);
        background-color: v.$color-lightgrey;
        text-decoration: none;

        // &:hover {
        //     color: v.$color-primary;
        //     border-color: v.$color-primary;
        // }
    }
}

// Pagination
.pagination-and-count {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .page-count {

    }
    .pagination {
        display: block;
        margin: (v.$block-margin * 2) 0;
        text-align: right;
    
        .page-numbers {
            min-width: 40px;
            min-height: 40px;
            padding: calc(v.$block-padding / 2) v.$block-padding;
            margin: calc(v.$block-margin / 4);
            background-color: v.$color-lightgrey;
            border-radius: v.$border-radius;
            text-decoration: none;
    
            &:hover {
                background-color: v.$color-primary;
                text-decoration: none;
                color: v.$color-white;
            }
    
            &.current {
                color: v.$color-darkgrey;
    
                &:hover {
                    background-color: v.$color-lightgrey;
                }
            }
        }
    }
}

// Emergency popup
#emergency-popup {
    display: none;
}