@use '../utils/variables' as v;
@use '../base/typography' as f;
@use '../utils/extends';

.card {
    display: flex;
    background-color: v.$color-white;
    border-radius: v.$border-radius-large;
    padding: v.$block-padding;

    .card-image {
        margin-bottom: v.$block-margin;

        img {
            width: 100%;
            aspect-ratio: 1/1;
            object-fit: cover;
            object-position: center;
            border-radius: v.$border-radius; 
        }
    }

    .title {
        a {
            &:hover {
                color: v.$color-secondary;
            }
        }
    }

    .excerpt {

    }
}

.people-result-card {
    @extend .card;
    flex-direction: column;
    padding: 0;
    flex-basis: 100%;
        
    @media #{v.$tablet} {
        flex-basis: calc(50% - #{v.$block-margin});
        max-width: calc(50% - #{v.$block-margin});
    }
    @media #{v.$laptop} {
        flex-basis: calc((100% / 3) - #{v.$block-margin});
        max-width: calc((100% / 3) - #{v.$block-margin});
    }
    @media #{v.$desktop} {
        flex-basis: calc((100% / 4) - #{v.$block-margin});
        max-width: calc((100% / 4) - #{v.$block-margin});
    }

    .card-image {
        img {
            border-radius: v.$border-radius-large v.$border-radius-large 0 0;
            aspect-ratio: 1/1.25;
        }
    }

    .card-content {
        padding: v.$block-padding;
        text-align: center;

        .title {
            font-size: f.$h3;
            margin-bottom: 0;
            font-weight: f.$font-bold;

            a {
                text-decoration: none;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .job-title {
            height: 28px;
            font-weight: f.$font-bold;
        }
    }
}

.insight-result-card {
    @extend .card;
    flex-direction: column;
    padding: 0;
    flex-basis: 100%;
        
    @media #{v.$tablet} {
        flex-basis: calc(50% - #{v.$block-margin});
        max-width: calc(50% - #{v.$block-margin});
    }
    @media #{v.$laptop} {
        flex-basis: calc((100% / 3) - #{v.$block-margin});
        max-width: calc((100% / 3) - #{v.$block-margin});
    }

    .card-image {
        margin-bottom: 0;

        img {
            aspect-ratio: 3/2;
            border-radius: v.$border-radius-large v.$border-radius-large 0 0;
        }
    }

    .card-content {
        padding: v.$block-padding;
        height: 100%;

        .categories {
            margin-bottom: v.$block-margin;
        }

        .title {
            color: v.$color-text;
            font-weight: f.$font-bold;
            text-align: left;
            font-size: f.$h4;
            margin-bottom: v.$block-margin;
            
            a {
                text-decoration: none;
            }
        }
    }

    .card-meta {
        padding: v.$block-padding;
        padding-top: 0;
        
        .tags {
            padding-bottom: v.$block-padding;
        }

        .card-author-date {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: v.$block-margin;

            time {
                font-size: f.$h6;
            }
        }
    }
}