@use '../utils/variables' as v;
@use '../base/typography' as f;
@use '../utils/mixins' as *;
@use '../utils/functions' as z;

form {
    position: relative;
    display: flex;
    flex-direction: column;
    background-color: v.$color-white;
    border-radius: v.$border-radius-large;
    padding: v.$block-padding * 2;

    .form-close {
        background: none;
        border: 0;
        display: none;
        position: absolute;
        top: v.$block-padding;
        right: v.$block-padding;
        width: 30px;
        fill: v.$color-text;
        cursor: pointer;
    }

    .field-row {
        margin: calc(v.$block-margin / 2) 0;

        &.required {
            >label {
                &:after {
                    content: ' (required)';
                    font-size: f.$font-size-body - 0.2;
                }
            }
        }
    }

    .title {
        font-size: f.$h3;
    }

    .intro {

    }

    .gdpr {
        margin: calc(v.$block-margin / 2) 0;
        font-size: f.$font-size-body - 0.2;
        color: v.$color-darkgrey;
    }
}

// Search and Filter forms
.searchandfilter {
   
}

// Form expand
.sidebar {
    form {
        .form-expand {
            display: none;
        }
    }
}

// CF7 forms
.wpcf7-form {
    padding-bottom: 75px; // for reCaptcha

    @media #{v.$tablet} {
        padding-bottom: v.$block-padding * 2;    
    }

    // Terms and conditions
    .wpcf7-acceptance {
        .wpcf7-list-item-label {
            line-height: 1.2rem;

            &:after {
                content: ' (required)';
                font-size: f.$font-size-body - 0.2;
            }
        }
    }

    .wpcf7-list-item {
        display: block;
    }

    .wpcf7-acceptance,
    .wpcf7-checkbox,
    .wpcf7-radio {
        label {
            display: flex;
            align-items: center;
        }
    }

    // Response messages
    .wpcf7-response-output {
        @include animation('hippyshake');
        border: 0 !important;
        border-radius: v.$border-radius;
        margin: calc(v.$block-margin / 2) auto;
        padding: v.$block-padding;
    }

    // Loader
    .ajax-loader {
        visibility: hidden;
        display: inline-block;
        background-color: #23282d;
        background-image: unset;
        opacity: 0.75;
        width: 24px;
        height: 24px;
        border: none;
        border-radius: 100%;
        padding: 0;
        margin: 0 24px;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            background-color: #fbfbfc;
            top: 4px;
            left: 4px;
            width: 6px;
            height: 6px;
            border: none;
            border-radius: 100%;
            transform-origin: 8px 8px;
            animation-name: spin;
            animation-duration: 1000ms;
            animation-timing-function: linear;
            animation-iteration-count: infinite;
        }
    }
}

form {
    &.sent,
    &.init,
    &.resetting,
    &.submitting {
        .wpcf7-response-output {
            display: none;
        }
    }

    &.failed,
    &.aborted,
    &.spam {
        .wpcf7-response-output {
            background-color: rgba(v.$color-red, 0.4) !important;
        } 
    }

    &.invalid,
    &.unaccepted {
        .wpcf7-response-output {
            border: 0 !important;
            background-color: rgba(v.$color-yellow, 0.4) !important;
        }
    }

}