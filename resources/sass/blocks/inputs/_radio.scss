@use '../../utils/variables' as v;

input[type="radio"] {
    position: relative;
    height: 24px;
    width: 24px;
    margin: 0;
    margin-right: 5px;
    padding: 10px;
    cursor: pointer;
    border-color: v.$color-primary-light;
    border-radius: 50%;
  
    // Dot
    &:after {
      content: '';
      position: absolute;
      top: 6px;
      left: 6px;
      width: 8px;
      height: 8px;
      border-radius: 4px;
      background: v.$color-white;
    }
  
    &:checked {
      background: v.$color-primary;
      border-color: v.$color-primary;
    }

    &:focus {
        outline: 2px solid v.$color-midgrey;
    }

    &:disabled {  
      &:checked {
        background: v.$color-darkgrey;
        border-color: v.$color-darkgrey;
      }
    }
  }
  