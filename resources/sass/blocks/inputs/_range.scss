@use '../../utils/variables' as v;

input[type='range'] {
    padding: 0;
    width: 100%;
    height: 5px;
    background-color: v.$color-primary;
    border-radius: 3px;
    border: none;
    margin: 15px 0;
    cursor: pointer;
  
    &::-webkit-slider-thumb {
      appearance: none;
      width: 30px;
      height: 30px;
      background-color: v.$color-white;
      border-radius: 15px;
      box-shadow: 0px 0px 3px v.$color-black;
      transition: all 0.5s ease;
      cursor: pointer;
    }
  }
  
  .range-handle {
    text-align: center;
  
    .range-value {
      display: block;
      font-weight: bold;
      padding: v.$block-padding;
      margin-top: calc(v.$block-margin / 2);
    }
  }
  