@use '../../utils/variables' as v;
@use '../../base/typography' as f;

input[type="submit"] {
    background-color: v.$color-primary;
    border-color: v.$color-transparent;
    color: v.$color-white;

    &:focus {
        outline: 2px solid v.$color-midgrey;
    }

    &:hover {
        cursor: pointer;
        background-color: v.$color-secondary;
        border-color: v.$color-transparent;
    }
}

button {
    line-height: 1.7;
}

.button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    outline: none;
    min-width: 150px;
    padding: calc(v.$site-padding / 2) v.$site-padding;
    margin: 2px 0;
    background-color: v.$color-primary;
    border-width: 2px;
    border-style: solid;
    border-color: v.$color-transparent;
    border-radius: v.$border-radius;
    color: v.$color-white;
    font-size: f.$font-size-body;
    text-decoration: none;
    transition: v.$transition;

    .icon {
        fill: v.$color-white;
        max-width: 25px;
        max-height: 25px;
        margin: 0 calc(v.$block-margin / 2);
    }

    &:focus {
        outline: 2px solid rgba(v.$color-primary, 0.6);
    }

    &:hover {
        cursor: pointer;
        color: v.$color-white;
        background-color: v.$color-secondary;
        border-color: v.$color-transparent;
        text-decoration: none;
    }
}

.button-secondary {
    @extend .button;
    background-color: v.$color-secondary;

    &:hover {
        background-color: v.$color-primary;
    }

    &:focus {
        outline: 2px solid rgba(v.$color-secondary, 0.6);
    }
}

.button-tertiary {
    @extend .button;
    background-color: v.$color-tertiary;

    &:hover {
        background-color: v.$color-primary;
    }

    &:focus {
        outline: 2px solid rgba(v.$color-tertiary, 0.6);
    }
}

.button-outline {
    @extend .button;
    background-color: v.$color-transparent;
    border-color: v.$color-text;
    color: v.$color-text;

    .icon {
        fill: v.$color-primary;
    }

    &:hover {
        background-color: v.$color-primary; 
        color: v.$color-white;

        .icon {
            fill: v.$color-white;
        }
    }
}

.button-link {
    @extend .button;
    background-color: v.$color-transparent;
    border: none;
    color: v.$color-text;

    .icon {
        fill: v.$color-primary;
    }

    &:hover {
        background-color: v.$color-primary; 
        color: v.$color-white;

        .icon {
            fill: v.$color-white;
        }
    }
}