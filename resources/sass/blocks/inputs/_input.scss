@use '../../utils/variables' as v;
@use '../../base/typography' as f;

input,
.input {
    appearance: none;
    letter-spacing: 0.1rem;
    background-color: v.$color-lightgrey;
    padding: v.$block-padding;
    font-size: f.$font-size-body + 0.2rem;
    border: 2px solid v.$color-white;
    border-radius: v.$border-radius;
    outline: none;
    width: 100%;
    max-width: 450px;
    transition: v.$transition;

    &:active,
    &:focus {
        border-color: v.$color-primary;
        outline: 0;
    }

    &[type="password"] {
        letter-spacing: 0.5rem;
    }

    &:disabled {
        background-color: v.$color-darkgrey;
        border-color: v.$color-darkgrey;
        cursor: not-allowed !important;
        opacity: .9;

        &:hover {
            background-color: v.$color-darkgrey;
            border-color: v.$color-darkgrey;
        }
    }
}