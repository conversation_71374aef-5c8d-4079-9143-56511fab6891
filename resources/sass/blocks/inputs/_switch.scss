@use '../../utils/variables' as v;

.switch {
    position: relative;
    display: inline-block;
    width: 80px;
    height: 39px;
    margin: 0;
  
    // Hide the checkbox
    input {
      opacity: 0;
      width: 80px;
      height: 39px;
      z-index: 2;
    }
  
    .switch-label {
      position: absolute;
      left: 100px;
      white-space: nowrap;
      top: 30%;
    }
  
    .switch-handle {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 40px;
      background-color: v.$color-midgrey;
      transition: v.$transition;
  
      &:before {
        content: '';
        position: absolute;
        height: 35px;
        width: 35px;
        left: 2px;
        bottom: 2px;
        border-radius: 33px;
        background-color: v.$color-white;
        transition: v.$transition;
      }
    }
  
    input:active + .switch-handle:before {
      width: 50px;
    }
  
    input:checked:active + .switch-handle:before {
      transform: translateX(40px);
      left: -13px;
    }
  
    input:checked + .switch-handle {
      background-color: v.$color-green;
    }
  
    input:focus + .switch-handle {
      box-shadow: 0 0 1px v.$color-green;
    }
  
    input:checked + .switch-handle:before {
      transform: translateX(40px);
    }
  }
  