@use '../../utils/variables' as v;

input[type="checkbox"] {
    position: relative;
    height: 24px;
    width: 24px;
    padding: 10px;
    margin: 0;
    margin-right: 5px;
    border-radius: 8px;
    border-color: v.$color-primary-light;
    cursor: pointer;
  
    // Tick
    &:after {
      content: '';
      display: block;
      position: absolute;
      left: 8px;
      top: 4px;
      opacity: 0;
      width: 5px;
      height: 9px;
      border: 2px solid v.$color-white;
      border-top: 0;
      border-left: 0;
      transition: v.$transition;
      transform: rotate(20deg);
    }
  
    &:checked {
      &:after {
        opacity: 1;
        transform: rotate(43deg);
      }
    }
  
    &:checked {
      background-color: v.$color-primary;
      border-color: v.$color-primary;
    }
  
    &:focus {
      box-shadow: 0 0 0 2px rgba(v.$color-primary-light, .3);
    }
  
    &:disabled {  
        &:checked {
            background-color: v.$color-darkgrey;
            border-color: v.$color-darkgrey;
        }
    }
}