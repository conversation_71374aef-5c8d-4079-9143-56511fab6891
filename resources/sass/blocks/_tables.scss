@use '../utils/variables' as v;
@use '../base/typography' as f;

table {
    max-width: calc(100% - #{v.$block-margin * 2});
    margin: v.$block-margin;
    border: v.$border;

    th,td,tr {
        border: v.$border;
    }

    th {
        color: v.$color-white;
        background-color: v.$color-primary;
        font-weight: f.$font-bold;
        padding: calc(v.$block-padding / 2);
    }

    td {
        padding: calc(v.$block-padding / 2);
    }

    tr {
        &:nth-type(odd) {
            background-color: v.$color-lightgrey;
        }
    }
}