@use '../utils/variables' as v;
@use '../utils/functions' as z;

.modal,
.cookie-consent {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: v.$block-padding;
  z-index: z.z(modal);
  width: 95%;
  height: auto;

  @media #{v.$nav-breakpoint} {
    max-width: 760px;
    top: 100px;
    border-radius: 9px;
    overflow: hidden;
  }

  .modal-inner {
    position: relative;
    background-color: v.$color-white;
    padding: v.$block-padding;
    padding-top: v.$block-padding * 3.5;

    @media #{v.$nav-breakpoint} {
      padding: v.$block-padding * 3;
    }

    .modal-close {
        background: none;
        border: none;
        position: absolute;
        top: v.$block-padding;
        right: v.$block-padding * 1.5;
        width: 30px;
        cursor: pointer;
        z-index: z.z(modal) + 1;

        svg {
          width: 25px;
          height: 25px;
        }
    }

    .modal-content {
      iframe {
        border: 0;
        width: 100%;
        min-height: 850px;
      }
    }
  }
}

body {
    &.modal {
      background-color: v.$color-white;
      
      .modal-content {
        scrollbar-gutter: stable;
        // padding-right: v.$block-padding * 0.5; // so scroll bar doesnt cover the content

        form {
          padding: 0;
        }
        
        input,
        textarea,
        select {
          max-width: 100%;
        }
        
        .title {
          display: none;
        }
      }
    }
  }