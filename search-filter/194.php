<?php if ($query->have_posts()) : ?>


    <div class="search-results-list">
        <p class="results-count">Found <span><?php echo $query->found_posts; ?> Results</span></p>
        <?php while ($query->have_posts()) :
            $query->the_post();
        ?>
            <?php component('/local/search/result-card'); ?>
        <?php endwhile; ?>
    </div>

    <div class="pagination-and-count">
        <p class="page-count">Page <?php echo $query->query['paged']; ?> of <?php echo $query->max_num_pages; ?></p>

        <?php if ($query->max_num_pages > 1) : ?>
            <div class="pagination">
                <?php echo pagination($query->max_num_pages); ?>
            </div>
        <?php endif; ?>
    </div>


<?php else : ?>
    <p>No Results Found</p>
<?php endif; ?>