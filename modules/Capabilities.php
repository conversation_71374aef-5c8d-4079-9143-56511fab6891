<?php

/**
 * User roles and permissions
 * 
 */

/**
 * Remove some user roles
 * 
 */
add_action('init', 'brabazon_role_remove');
function brabazon_role_remove()
{
    $roles = [
        'author',
        'contributor',
        'subscriber',
        'wpseo_editor'
    ];

    foreach ($roles as $role) {
        remove_role($role);
    }
}

/**
 * Remove editor capabilities
 * 
 */
add_action('init', 'remove_editor_capability');
function remove_editor_capability()
{
    $editor = get_role('editor');

    $caps = array(
        'moderate_comments',
        'update_themes',
        'install_themes',
        'edit_themes',
        'switch_themes'
    );

    foreach ($caps as $cap) {
        if ($editor->has_cap($cap)) {
            $editor->remove_cap($cap);
        }
    }
}

/**
 * Remove access to admin accounts from non-admins
 * 
 */
add_action('pre_user_query', 'stop_admin_accout_access');
function stop_admin_accout_access($user_search)
{
    if (!current_user_can('administrator')) {
        global $wpdb;

        $user_search->query_where = str_replace(
            'WHERE 1=1',
            "WHERE 1=1 AND {$wpdb->users}.ID IN (
              SELECT {$wpdb->usermeta}.user_id FROM $wpdb->usermeta 
              WHERE {$wpdb->usermeta}.meta_key = '{$wpdb->prefix}capabilities'
              AND {$wpdb->usermeta}.meta_value NOT LIKE '%administrator%' )",
            $user_search->query_where
        );
    }
}

/**
 * Add editor capabilities
 * 
 */
add_action('init', 'add_editor_capability', 11);
function add_editor_capability()
{
    $editor = get_role('editor');

    $caps = array(
        'edit_theme_options'
    );

    foreach ($caps as $cap) {
        if (!$editor->has_cap($cap)) {
            $editor->add_cap($cap, true);
        }
    }

    if (current_user_can('editor')) {
        /**
         * Add access to redirects
         * 
         */
        add_filter('redirection_role', function ($role) {
            return 'edit_posts';
        });

        /**
         * Removes admin searches
         * 
         */
        add_filter('relevanssi_admin_search_capability', function ($capability) {
            return 'manage_options';
        });
    }
}

/**
 * Removes access to pages
 * 
 */
add_action('admin_init', 'remove_access_to_pages');
function remove_access_to_pages()
{
    global $pagenow;

    if (!current_user_can('administrator')) {

        $pages = [
            'edit-comments.php',
            'themes.php'
        ];

        foreach ($pages as $page) {
            if ($pagenow === $page) {
                wp_safe_redirect(admin_url());
                exit;
            }

            remove_menu_page($page);
        }
    }
}
