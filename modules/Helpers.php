<?php

/**
 * A pretty way to display var_dumps
 * 
 */
function dd($item)
{
	echo "<pre>";
	var_dump($item);
	echo "</pre>";
}

/**
 * Check if a variable is defined and has content
 * 
 */
if (!function_exists('exists')) {
	function exists($variable)
	{
		if (isset($variable) && strlen($variable > 1)) {
			return true;
		}

		return false;
	}
}

/**
 * Remove spaces in a string
 * 
 */
function spaceless(String $string)
{
	$new_string = trim(str_replace(' ', '', $string));

	return $new_string;
}

/**
 * Get the public asset directory
 * 
 */
function asset_url($file)
{
	return get_stylesheet_directory_uri() . '/assets/' . ltrim($file);
}

// @TODO test
/**
 * Show a 404
 * 
 */
if (!function_exists('show_404')) {
	function show_404($error = 'There has been an error.')
	{
		if (is_admin()) {
			echo $error;
		} else {
			global $wp_query;
			$wp_query->set_404();
			status_header(404);
			get_template_part(404);
			exit();
		}
	}
}

/**
 * Shorthand way of getting template parts
 * 
 */
if (!function_exists('component')) {
	function component($path, $args = [])
	{

		foreach ($args as $key => $value) {
			set_query_var($key, $value);
		}

		get_template_part("components/{$path}", null, $args);
	}
}

/**
 * Gets the ID of the top most parent
 * Useful for getting the parent title etc in subnavs
 */
function get_top_parent_id()
{
	global $post;

	$id = null;

	$parents = get_post_ancestors($post);
	$id = $post->ID;

	if (!empty($parents)) {
		$id = array_pop($parents);
	}

	return $id;
}

/**
 * Structure address
 * 
 */
if (!function_exists('office_address')) {
	function office_address($postId)
	{
		$get_address = get_field('office_location', $postId);

		$address = '';

		$street_number = $get_address['street_number'] ?? null;
		$street_name = $get_address['street_name'] ?? null;
		$city = $get_address['city'] ?? null;
		$post_code = $get_address['post_code'] ?? null;
		$country = $get_address['country'] ?? null;
		$dx_address = $get_address['dx_address'] ?? null;

		if ($street_number && $street_name) {
			$address .= '<span itemprop="streetAddress">' . $street_number . ' ' . $street_name . '</span>';
		} else {
			if ($street_number) {
				$address .= $street_number . ' ';
			}

			if ($street_name) {
				$address .= $street_name . '<br/>';
			}
		}

		if ($city) {
			$address .= '<span itemprop="addressLocality">' . $city . '</span>';
		}

		if ($post_code) {
			$address .= '<span itemprop="postalCode">' . $post_code  . '</span>';
		}

		if ($country) {
			$address .= $country . '<br/>';
		}

		if ($dx_address) {
			$address .= $dx_address;
		}

		return $address;
	}
}

/**
 * Structure office contact details
 * 
 */
if (!function_exists('office_contact')) {
	function office_contact($postId)
	{
		$contact = '';

		$phone_number = get_field('phone_number', $postId) ?? null;
		$fax_number = get_field('fax_number', $postId) ?? null;
		$office_email = get_field('office_email', $postId) ?? null;

		if ($phone_number) {
			$contact .= '<div class="phone-number"><a href="tel:' . spaceless($phone_number) . '" itemprop="telephone">' . $phone_number . '</a></div>';
		}

		if ($fax_number) {
			$contact .= '<div class="faxnumber" itemprop="faxNumber">' . $fax_number . '</div>';
		}

		if ($office_email) {
			$contact .= '<div class="office-email"><a class="email" href="' . base64_encode($office_email) . '" itemprop="email">' . base64_encode($office_email) . '</a></div>';
		}

		return $contact;
	}
}

/**
 * Get an Icon from the icons folder and setup the html
 * https://css-tricks.com/svg-use-with-external-reference-take-2/
 */
if (!function_exists('icon')) {
	function icon($icon, $class = "")
	{
		return '<svg class="icon ' . $class . '"><use xlink:href="' . asset_url('images/icons/icons.svg#icon-' . $icon) . '"></use></svg>';
	}
}

/**
 * Checks if something exists
 * 
 */
function exists($var)
{
	if (!isset($var) || empty($var)) {
		return false;
	}

	return true;
}

/**
 * Trims text to a defined length
 * 
 */
function truncate($content, $length, $dots = "...")
{
	return (strlen($content) > $length) ? substr($content, 0, $length - strlen($dots)) . $dots : $content;
}

/**
 * Checks if the page you are on is the one youre checking against
 * 
 */
function page($page)
{
	global $post;

	if ($page === $post->post_name) {
		return true;
	}

	return false;
}


/**
 * Show the post categories
 * 
 */
function show_the_categories()
{
	$categories = get_the_category();

	if (exists($categories)) {
		echo '<div class="categories">';
		foreach ($categories as $category) {
			echo '<a class="category" href="/insights/?_sft_category=' . $category->slug . '" rel="category">' . $category->cat_name . '</a>';
		};
		echo '</div>';
	}
}


/**
 * Show the post tags
 * 
 */
function show_the_tags()
{
	$tags = get_the_tags();

	if (exists($tags)) {
		echo '<div class="tags">';
		foreach ($tags as $tag) {
			echo '<span class="tag" rel="tag">' . $tag->name . '</span>';
			//echo '<a class="tag" href="' . get_permalink(get_option('page_for_posts')) . '?_sft_post_tag=' . $tag->slug . '" rel="tag">' . $tag->name . '</a>';
		};
		echo '</div>';
	}
}

/**
 * Get the page type
 * 
 */
if (!function_exists('get_page_type')) {
	function get_page_type($postId = null)
	{
		$type = 'default';

		if (null !== $postId) {
			$ID = $postId;
		} else {
			$ID = get_the_ID();
		}

		$get_term = get_the_terms($ID, 'page_type');

		if (exists($get_term)) {
			$slug = $get_term[0]->slug;
			$type = $slug;
		}

		return $type;
	}
}

/**
 * Create a lazyload function where images are consistant
 * 
 */
if (!function_exists('lazyload')) {
	function lazyload($src, $alt = '', $class = '')
	{
		return "<img class=\"lazy {$class}\" data-src=\"{$src}\" alt=\"{$alt}\" />";
	}
}
