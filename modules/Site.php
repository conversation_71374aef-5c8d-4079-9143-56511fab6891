<?php

/**
 * Add globals section to the CMS
 * 
 */
add_action('wp_head', function () {
	global $post;
});

/**
 * Add globals section to the CMS
 * 
 */
if (function_exists('acf_add_options_page')) {
	acf_add_options_page([
		'page_title' => 'Globals',
		'menu_title' => 'Globals',
		'menu_slug' => 'global-options',
		'capability' => 'edit_posts'
	]);
}

/**
 * Remove default WordPress inline styling
 * 
 */
add_action('wp_enqueue_scripts', 'remove_global_styles');
function remove_global_styles()
{
	wp_dequeue_style('global-styles');
	wp_dequeue_style('wp-block-library');
	wp_dequeue_style('classic-theme-styles');
	wp_dequeue_style('search-filter-plugin-styles');
	wp_dequeue_style('contact-form-7');
}

/**
 * Remove emojis from the website
 * 
 */
add_action('init', 'disable_emojis');
function disable_emojis()
{
	remove_action('wp_head', 'print_emoji_detection_script', 7);
	remove_action('admin_print_scripts', 'print_emoji_detection_script');
	remove_action('wp_print_styles', 'print_emoji_styles');
	remove_action('admin_print_styles', 'print_emoji_styles');
	remove_filter('the_content_feed', 'wp_staticize_emoji');
	remove_filter('comment_text_rss', 'wp_staticize_emoji');
	remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
	add_filter('tiny_mce_plugins', 'disable_emojis_tinymce');
	add_filter('wp_resource_hints', 'disable_emojis_remove_dns_prefetch', 10, 2);
}
function disable_emojis_tinymce($plugins)
{
	if (is_array($plugins)) {
		return array_diff($plugins, array('wpemoji'));
	} else {
		return array();
	}
}
function disable_emojis_remove_dns_prefetch($urls, $relation_type)
{
	if ('dns-prefetch' == $relation_type) {
		$emoji_svg_url = apply_filters('emoji_svg_url', 'https://s.w.org/images/core/emoji/2/svg/');

		$urls = array_diff($urls, array($emoji_svg_url));
	}
	return $urls;
}

/**
 * Remove default WordPress site health widget
 * 
 */
add_action('wp_dashboard_setup', 'remove_site_health_dashboard_widget');
function remove_site_health_dashboard_widget()
{
	remove_meta_box('dashboard_site_health', 'dashboard', 'normal');
}

/**
 * Add Brabazon Digital link to Admin toolbar
 * 
 */
if (!function_exists('get_help_button')) {
	add_action('admin_bar_menu', 'get_help_button', 999);
	function get_help_button($wp_admin_bar)
	{
		$args = array(
			'id' => 'get-help',
			'title' => 'Get help from Brabazon Digital',
			'href' => 'https://brabazondigital.com/help/',
			'meta' => array(
				'title' => 'Get help from Brabazon',
				'target' => '_blank'
			)
		);
		$wp_admin_bar->add_node($args);
	}
}

/**
 * Add Menu item to the CMS
 * 
 */
add_theme_support('menus');

/**
 * Add classes to the body tag
 * 
 */
add_filter('body_class', function ($classes) {
	return array_merge($classes, body_classes());
});
function body_classes()
{
	global $post;

	$classes = [];
	$post_type = get_post_type();
	$page_type = ($terms = get_the_terms($post->ID, 'page_type')) ? $terms[0]->slug : 'default';
	$post_name = sanitize_title($post->post_name);
	$post_id = $post->ID;


	$classes[] = "post-type-{$post_type}";
	$classes[] = "page-type-{$page_type}";
	$classes[] = "post-name-{$post_name}";
	$classes[] = "post-id-{$post_id}";

	return $classes;
}

/**
 * Remove the H1 option from the CMS WYSIWYG editor
 * 
 */
add_filter('tiny_mce_before_init', 'my_format_TinyMCE');
function my_format_TinyMCE($in)
{
	$in['block_formats'] = "Paragraph=p; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6; Preformatted=pre";
	return $in;
}

/**
 * Customise the breadcrumbs
 * 
 */
if (!function_exists('change_yoast_breadcrumbs_output')) {
	add_filter('wpseo_breadcrumb_output', 'change_yoast_breadcrumbs_output');

	function change_yoast_breadcrumbs_output($output)
	{
		$output = '<nav aria-label="breadcrumbs" class="breadcrumbs"><ul class="breadcrumbs-list" role="list">' . $output . '</ul></nav>';
		$output = preg_replace('#<span(.*?)>(.*?)</span>#', '$2', $output);
		$output = str_replace('<span', '<li', $output);
		$output = str_replace('</span>', '</li>', $output);
		return $output;
	}
}

/**
 * Add custom page types
 * 
 */
if (!function_exists('register_taxonomies')) {
	add_action('init', 'register_taxonomies');

	function register_taxonomies()
	{

		// Page type
		register_taxonomy('page_type', 'page', array(
			'labels' => array(
				'name'              => 'Page Types',
				'singular_name'     => 'Page Type',
				'search_items'      => 'Search Page Types',
				'all_items'         => 'All Page Types',
				'view_item'         => 'View Page Type',
				'edit_item'         => 'Edit Page Type',
				'update_item'       => 'Update Page Type',
				'add_new_item'      => 'Add New Page Type',
				'new_item_name'     => 'New Page Type Name',
				'not_found'         => 'No Page Types Found',
				'menu_name'         => 'Page Types',
			),
			'hierarchical'      => true,
			'public'            => true,
			'show_admin_column' => true,
			'show_in_rest'      => true,
		));

		// Office
		register_taxonomy('offices', 'page', array(
			'labels' => array(
				'name'              => 'Offices',
				'singular_name'     => 'Office',
				'search_items'      => 'Search Offices',
				'all_items'         => 'All Offices',
				'view_item'         => 'View Office',
				'edit_item'         => 'Edit Office',
				'update_item'       => 'Update Office',
				'add_new_item'      => 'Add New Office',
				'new_item_name'     => 'New Office Name',
				'not_found'         => 'No Offices Found',
				'menu_name'         => 'Offices',
			),
			'hierarchical'      => true,
			'public'            => true,
			'show_admin_column' => true,
			'show_in_rest'      => true,
		));

		// Services
		register_taxonomy('services', ['page', 'post'], array(
			'labels' => array(
				'name'              => 'Services',
				'singular_name'     => 'Service',
				'search_items'      => 'Search Services',
				'all_items'         => 'All Services',
				'view_item'         => 'View Service',
				'edit_item'         => 'Edit Service',
				'update_item'       => 'Update Service',
				'add_new_item'      => 'Add New Service',
				'new_item_name'     => 'New Service Name',
				'not_found'         => 'No Services Found',
				'menu_name'         => 'Services',
			),
			'hierarchical'      => true,
			'public'            => true,
			'show_in_rest'      => true,
			'show_admin_column' => true,
		));
	}
}

/**
 * Add support for featured image
 * 
 */
add_theme_support('post-thumbnails', array(
	'post',
	'page',
	// 'custom-post-type-name',
));

/**
 * Removes the un-needed <p> tags from Contact Form 7
 * 
 */
add_filter('wpcf7_autop_or_not', '__return_false');

/**
 * Add the Google API Key to ACF
 * 
 */
add_filter('acf/fields/google_map/api', 'my_acf_google_map_api');
function my_acf_google_map_api($api)
{
	$api['key'] = get_field('google_maps', 'option');

	return $api;
}

/**
 * Register the modal template
 * 
 */
add_rewrite_endpoint('modal', EP_PAGES);
add_rewrite_endpoint('theme', EP_PAGES);

add_filter('template_include', 'modal_template', 99);
function modal_template($template)
{
	global $wp_query;

	if (isset($wp_query->query_vars['modal'])) {

		$new_template = locate_template('components/' . $wp_query->query_vars['theme'] . '.php');

		if ('' != $new_template) {

			show_admin_bar(false);
			return $new_template;
		}
	}
	return $template;
}

/**
 * Custom pagination
 * 
 */
function pagination($total_pages)
{

	$bignum = *********;

	if ($total_pages <= 1) {
		return;
	}

	return paginate_links([
		'base' => str_replace($bignum, '%#%', get_pagenum_link(1) . '&sf_paged=' . $bignum),
		'format' => '&sf_paged=%#%',
		'current'      => max(1, get_query_var('paged')),
		'total'        => $total_pages,
	]);
}

/**
 * Custom Taxonomy filters in the Admin area
 * 
 */
add_action('restrict_manage_posts', 'add_pages_taxonomy_filters', 10, 2);
function add_pages_taxonomy_filters($post_type)
{

	// Apply this only on a specific post type
	if ('page' !== $post_type) {
		return;
	}

	// A list of taxonomy slugs to filter by
	$taxonomies = [
		'page_type',
	];

	foreach ($taxonomies as $taxonomy_slug) {

		// Retrieve taxonomy data
		$taxonomy_obj = get_taxonomy($taxonomy_slug);
		$taxonomy_name = $taxonomy_obj->labels->name;

		// Retrieve taxonomy terms
		$terms = get_terms($taxonomy_slug);

		// Display filter HTML
		echo "<select name='{$taxonomy_slug}' id='{$taxonomy_slug}' class='postform'>";
		echo '<option value="">' . sprintf(esc_html__('Show All %s', 'text_domain'), $taxonomy_name) . '</option>';
		foreach ($terms as $term) {
			printf(
				'<option value="%1$s" %2$s>%3$s (%4$s)</option>',
				$term->slug,
				((isset($_GET[$taxonomy_slug]) && ($_GET[$taxonomy_slug] == $term->slug)) ? ' selected="selected"' : ''),
				$term->name,
				$term->count
			);
		}
		echo '</select>';
	}
}

/**
 * Removes comments from admin column/filter
 * 
 */
function disable_comments()
{
	$post_types = get_post_types();
	foreach ($post_types as $post_type) {
		if (post_type_supports($post_type, 'comments')) {
			remove_post_type_support($post_type, 'comments');
			remove_post_type_support($post_type, 'trackbacks');
		}
	}
}
add_action('admin_init', 'disable_comments');

/**
 * Add menu link in the sidebar for convenience
 * 
 */
add_action('admin_menu', 'add_menus_link');
function add_menus_link()
{
	if (!current_user_can('administrator')) {
		add_menu_page('menus_link', 'Menus', 'read', 'nav-menus.php', '', 'dashicons-menu', 20);
	}
}

/**
 * Removes widgets from dashboard
 * 
 */
add_action('wp_dashboard_setup', 'remove_dashboard_widgets');
function remove_dashboard_widgets()
{
	global $wp_meta_boxes;

	unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_incoming_links']);
	unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_right_now']);
	unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_plugins']);
	unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_site_health']);
	unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_recent_drafts']);
	unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_recent_comments']);
	unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_php_nag']);
	unset($wp_meta_boxes['dashboard']['normal']['core']['welcome_panel']);
	unset($wp_meta_boxes['dashboard']['normal']['high']['rank_math_dashboard_widget']);
	unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_quick_press']);
	unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_primary']);
	unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_secondary']);
}

/**
 * Excludes pages set to no-index in Yoast
 * 
 */
add_filter('get_pages', 'lwd_exclude_no_indexes_from_wp_list_pages', 10, 2);
function lwd_exclude_no_indexes_from_wp_list_pages($pages, $args)
{
	if (array_key_exists('walker', $args)) {
		foreach ($pages as $key => $item) {
			$_yoast_wpseo_meta_robots_noindex = get_post_meta($item->ID, '_yoast_wpseo_meta-robots-noindex', true);
			if ($_yoast_wpseo_meta_robots_noindex == 1) unset($pages[$key]);
		}
	}
	return $pages;
}

/**
 * Move Yoast to bottom of the page in admin
 * 
 */
add_filter('wpseo_metabox_prio', 'yoasttobottom');
function yoasttobottom()
{
	return 'low';
}

/**
 * Remove buttons from admin bar
 * 
 */
add_action('wp_before_admin_bar_render', 'remove_buttons_from_admin_bar');
function remove_buttons_from_admin_bar()
{
	global $wp_admin_bar;

	$wp_admin_bar->remove_menu('customize'); // Customize button
	$wp_admin_bar->remove_menu('comments'); // Comments button
}

/**
 * Add media file size notice
 * 
 */
add_action('admin_notices', 'wp_media_notice_info');
function wp_media_notice_info()
{
	global $pagenow;

	if ('upload.php' == $pagenow || 'media-new.php' == $pagenow || 'media-upload' == $pagenow) {
		echo '<div class="notice notice-info"><p>Info: You may need to reduce the image size. <a href="https://tinywow.com/image/compress" target="_blank">Click here</a> to compress your image to make sure it\'s below the limit.</p></div>';
	}
}

/**
 * Remove srcset from the frontend and remove the ability for Wordpress to create loads of image sizes
 * 
 */
function remove_max_srcset_image_width($max_width)
{
	return false;
}
add_filter('max_srcset_image_width', 'remove_max_srcset_image_width');

function wdo_disable_srcset($sources)
{
	return false;
}
add_filter('wp_calculate_image_srcset', 'wdo_disable_srcset');

add_filter('big_image_size_threshold', '__return_false');
