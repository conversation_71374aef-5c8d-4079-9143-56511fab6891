<?php

/**
 * Create a icon shortcut for the frontend
 * [icon icon="chevron-right"]
 */
add_shortcode('icon', 'icon_shortcode');
function icon_shortcode($atts)
{
    $default = array(
        'icon' => ''
    );

    $a = shortcode_atts($default, $atts);

    return icon($a['icon']);
}

/**
 * Create a icon shortcut for the year
 * [year]
 */
add_shortcode('year', 'year_shortcode');
function year_shortcode()
{
    return date('Y');
}

/**
 * Shows the sitemap without the excluded pages set in Yoast
 * [lwd_no_plug_html_sitemap]
 */
add_shortcode('lwd_no_plug_html_sitemap', 'lwd_no_plug_html_sitemap');
function lwd_no_plug_html_sitemap()
{
    $pages = wp_list_pages([
        'title_li' => '',
        'sort_column' => 'menu_order'
    ]);
    return $pages;
}
