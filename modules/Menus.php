<?php

/**
 * Register our custom menus
 * 
 */
if (!function_exists('register_menus')) {
    add_action('init', 'register_menus');
    function register_menus()
    {
        register_nav_menus(array(
            'main_menu' => 'Main Menu',
            'mobile_menu'  => 'Mobile Menu',
            'footer_menu'  => 'Footer Menu',
            'footer_legal_menu'  => 'Footer Legal Menu'
        ));
    }
}

/**
 * Checks to see if the page has children
 * 
 */
if (!function_exists('has_children')) {
    add_shortcode('wpb_childpages', 'has_children');
    function has_children($post_id)
    {
        $args = array(
            'post_parent' => $post_id,
        );

        $children = get_children($args);

        if (!empty($children)) {
            return true;
        }

        return false;
    }
}

/**
 * Builds a sub-nav based on the parent
 * 
 */
if (!function_exists('sub_nav')) {
    add_shortcode('wpb_childpages', 'sub_nav');
    function sub_nav()
    {
        global $post;

        // $has_children = get_children( $post->ID );

        if (has_children($post->ID)) {
            $childpages = wp_list_pages('sort_column=menu_order&title_li=&child_of=' . $post->ID . '&echo=0&depth=1');
        } else {
            $childpages = wp_list_pages('sort_column=menu_order&title_li=&child_of=' . $post->post_parent . '&echo=0&depth=1');
        }

        if ($childpages) {
            $string = '<ul>' . $childpages . '</ul>';
        }

        return $string;
    }
}
