<?php component('/core/content/banner/banner'); ?>

<div class="main-content">
    <div class="container">
        <article class="main-article">
            <?php component('/core/breadcrumbs'); ?>

            <?php the_content(); ?>

            <div id="map" class="map">
                <?php $location = get_field('office_location'); ?>
                <span class="title"><?php echo the_title(); ?></span>

                <?php if ($location) : ?>
                    <div class="map-meta">
                        <span class="office-zoom hidden"><?php echo $location['zoom']; ?></span>
                        <span class="office-name hidden"><?php echo get_field('office_name'); ?></span>
                        <span class="office-address"><?php echo $location['address']; ?></span>
                        <span class="lat hidden"><?php echo $location['lat']; ?></span>
                        <span class="lon hidden"><?php echo $location['lng']; ?></span>
                    </div>
                <?php endif; ?>
            </div>

            <?php component('local/offices/contact', ['office' => $post]); ?>

            <?php echo the_field('office_additional'); ?>
        </article>

        <?php component("/core/content/sidebar/sidebar"); ?>
    </div>
</div>