<?php $darken = get_field('darken_banner'); ?>

<section class="banner">
    <?php the_post_thumbnail('large'); ?>

    <div class="container">
        <div class="profile-banner-content">
            <h1><?php the_title(); ?></h1>
            <span class="job-title"><?php echo $post->job_title; ?></span>

            <?php
            $telephone = $post->telephone;
            $mobile = $post->mobile;
            $email = $post->email;
            ?>

            <?php if ($telephone) : ?>
                <a href="tel:<?php echo spaceless($telephone); ?>" class="telephone"><?php echo icon('phone'); ?> <?php echo $telephone; ?></a>
            <?php endif; ?>

            <?php if ($mobile) : ?>
                <a href="tel:<?php echo spaceless($mobile); ?>" class="mobile"><?php echo icon('phone'); ?> <?php echo $mobile; ?></a>
            <?php endif; ?>

            <?php if ($email) : ?>
                <a class="email has-icon" id="email" href="<?php echo base64_encode($email); ?>"><?php echo icon('email'); ?>Email</a>
            <?php endif; ?>

            <?php if (have_rows('social_accounts')) : ?>

                <div id="social-icons" class="social-icons">
                    <?php while (have_rows('social_accounts')) : the_row();
                        $type = get_sub_field('social_network');
                        $link = get_sub_field('social_link');
                    ?>
                        <a class="social-link icon-<?php echo $type; ?>" href="<?php echo $link; ?>" target="_blank" title="Visit our <?php echo ucwords($type); ?> account"><?php echo icon($type); ?></a>
                    <?php endwhile; ?>
                </div>

            <?php endif; ?>
        </div>
    </div>
</section>