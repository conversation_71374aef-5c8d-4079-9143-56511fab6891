<?php
$darken = get_field('darken_banner');
?>

<section class="banner<?php echo $darken ? ' darken' : ''; ?>">
    <?php
    if (has_post_thumbnail()) {
        the_post_thumbnail();
    } else {
        $exclude_banner_image = get_field('hide_fallback_banner_image');

        if (false === $exclude_banner_image) {
            $fallback_image = get_field('fallback_banner_image', 'option');
            $fallback_image_url = wp_get_attachment_image_src($fallback_image, 'full');

            // Set the fallabck image to the featured image
            // update_post_meta($post->ID, '_thumbnail_id', $fallback_image);

            echo '<img src="' . $fallback_image_url[0] . '" />';
        }
    };
    ?>

    <div class="content-wrapper">
        <div class="container">
            <h1><?php the_title(); ?></h1>
        </div>
    </div>
</section>