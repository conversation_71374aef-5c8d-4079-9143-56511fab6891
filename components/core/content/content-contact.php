<?php component('/core/content/banner/banner'); ?>

<div class="main-content">
    <div class="container">
        <article class="main-article">
            <?php component('/core/breadcrumbs'); ?>

            <?php the_content(); ?>

            <?php
            $args = array(
                'post_type' => 'page',
                'orderby' => 'menu_order',
                'order' => 'ASC',
                'posts_per_page' => -1,
                'tax_query' => array(
                    array(
                        'taxonomy' => 'page_type',
                        'field'    => 'slug',
                        'terms'    => ['office']
                    )
                )
            );

            $offices = get_posts($args);
            ?>

            <div id="map" class="map">
                <?php foreach ($offices as $office) : ?>
                    <?php $location = get_field('office_location', $office->ID); ?>
                    <span class="title"><?php echo the_title(); ?></span>

                    <?php if ($location) : ?>
                        <div class="map-meta">
                            <span class="office-zoom hidden"><?php echo $location['zoom']; ?></span>
                            <span class="office-name hidden"><?php echo get_field('office_name', $office->ID); ?></span>
                            <span class="office-address"><?php echo $location['address']; ?></span>
                            <span class="lat hidden"><?php echo $location['lat']; ?></span>
                            <span class="lon hidden"><?php echo $location['lng']; ?></span>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>

            <?php

            if ($offices) {
                foreach ($offices as $office) {
                    component('local/offices/contact', ['office' => $office]);
                }
            }

            wp_reset_postdata();
            ?>

        </article>

        <?php component("/core/content/sidebar/sidebar"); ?>
    </div>
</div>