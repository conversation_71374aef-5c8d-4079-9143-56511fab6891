<?php
$show_call_us = get_field('show_call_us', 'option');
$number_text = 'Call us';

// Get the number
$number_location = get_field('global_cta_location', 'option');

if ('custom_number' === $number_location) {
    $cta_number = get_field('global_cta_number', 'option');
} else {
    // Get number from main office
    $main_office_id = get_field('main_office', 'option');
    $main_office_number = get_field('phone_number', $main_office_id[0]);
    $cta_number = $main_office_number;
}

// Decide what to show on the button
if (false === $show_call_us) {
    $number_text = $cta_number;
}
?>

<div class="call-to-actions">
    <div class="container">
        <a href="tel:<?php echo spaceless($cta_number); ?>" class="button-secondary"><?php echo $number_text; ?></a>
        <a href="/contact/conveyancing-quote/" data-trigger="modal" class="button-secondary">Conveyancing Quote</a>
        <a href="/contact/get-in-touch" data-trigger="modal" class="button-secondary">Get in touch</a>
    </div>
</div>