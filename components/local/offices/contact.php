<div class="office-list office-<?php echo $office->post_name; ?>" itemscope itemtype="http://schema.org/Organization">
    <div class="office-details">
        <a href="<?php echo get_permalink($office->ID); ?>">
            <h3><?php echo get_field('office_name', $office->ID); ?></h3>
        </a>

        <div class="office-address" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">
            <?php echo office_address($office->ID); ?>
        </div>
    </div>

    <div class="office-contact">
        <?php echo office_contact($office->ID); ?>
    </div>

    <?php if (have_rows('office_hours', $office->ID)) : ?>
        <div class="office-schedule" itemprop="openingHoursSpecification" itemscope itemtype="http://schema.org/OpeningHoursSpecification">
            <h4>Opening hours</h4>

            <?php while (have_rows('office_hours', $office->ID)) : the_row();
                $day = get_sub_field('opening_day');
                $opening_hours = get_sub_field('opening_hours');
                $closing_hours = get_sub_field('closing_hours');
                $closed = get_sub_field('office_closed');
            ?>
                <div>
                    <?php if ($closed) : ?>
                        <?php echo ucwords($day); ?>: Closed
                    <?php else : ?>
                        <meta itemprop="dayOfWeek" content="<?php echo $day; ?>">
                        <meta itemprop="opens" content="<?php echo $opening_hours; ?>">
                        <meta itemprop="closes" content="<?php echo $closing_hours; ?>">
                        <?php echo ucwords($day); ?>: <?php echo $opening_hours; ?> – <?php echo $closing_hours; ?>
                    <?php endif; ?>
                </div>
            <?php endwhile; ?>
        </div>
    <?php endif; ?>

</div>