<?php if (get_field('footer_office', 'option')) : ?>

    <?php
    $footer_office = get_field('footer_office', 'option');
    $address = '';

    $office_location = get_field('office_location', $footer_office[0]->ID);

    $street_number = $office_location['street_number'] ?? null;
    $street_name = $office_location['street_name'] ?? null;
    $city = $office_location['city'] ?? null;
    $post_code = $office_location['post_code'] ?? null;
    $country = $office_location['country'] ?? null;
    $dx_address = $office_location['dx_address'] ?? null;

    if ($street_number && $street_name) {
        $address .= '<span itemprop="streetAddress">' . $street_number . ' ' . $street_name . '</span>';
    } else {
        if ($street_number) {
            $address .= $street_number . ' ';
        }

        if ($street_name) {
            $address .= $street_name . '<br/>';
        }
    }

    if ($city) {
        $address .= '<span itemprop="addressLocality">' . $city . '</span>';
    }

    if ($post_code) {
        $address .= '<span itemprop="postalCode">' . $post_code  . '</span>';
    }

    if ($country) {
        $address .= $country . '<br/>';
    }

    if ($dx_address) {
        $address .= $dx_address;
    }
    ?>

    <?php
    $contact = '';

    $phone_number = get_field('phone_number', $footer_office[0]->ID) ?? null;
    $fax_number = get_field('fax_number', $footer_office[0]->ID) ?? null;
    $office_email = get_field('office_email', $footer_office[0]->ID) ?? null;

    if ($phone_number) {
        $contact .= '<div class="phone-number"><a href="tel:' . spaceless($phone_number) . '" itemprop="telephone">' . $phone_number . '</a></div>';
    }

    if ($fax_number) {
        $contact .= '<div class="faxnumber" itemprop="faxNumber">' . $fax_number . '</div>';
    }

    if ($office_email) {
        $contact .= '<div class="office-email"><a class="email" href="' . base64_encode($office_email) . '" itemprop="email">Email</a></div>';
    }
    ?>


    <div>
        <div class="office-address" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">
            <a class="office-name" href="<?php echo get_permalink($footer_office[0]->ID); ?>"><?php the_field('office_name', $footer_office[0]->ID); ?></a>
            <?php echo $address; ?>
        </div>

        <div class="office-contact">
            <?php echo $contact; ?>
        </div>
    </div>
<?php endif; ?>