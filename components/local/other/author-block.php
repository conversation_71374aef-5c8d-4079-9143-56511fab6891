<?php $author = get_field('author'); ?>

<div class="author-block">
    <?php if ($author) : ?>
        <?php $image = get_field('result_card_image', $author->ID); ?>

        <a href="<?php the_permalink($author->ID); ?>">
            <?php if ($image) : ?>
                <?php echo lazyload(esc_url($image['url']), $author->post_title . ' profile image', 'avatar'); ?>
            <?php else : ?>
                <?php echo lazyload(esc_url(asset_url('images/placeholders/people-search-placeholder.jpg')), 'Placeholder image for people result card', 'avatar'); ?>
            <?php endif; ?>
        </a>
        <div class="author-name">
            <a href="<?php the_permalink($author->ID); ?>"><?php echo $author->post_title; ?></a>
            <p class="job-title"><?php the_field('job_title', $author->ID); ?></p>
        </div>
    <?php else : ?>
        <img class="avatar" src="<?php echo asset_url('images/placeholders/people-search-placeholder.jpg'); ?>" alt="Placeholder image for people result card" />
        <div class="author-name">
            <?php echo get_bloginfo('name'); ?>
        </div>
    <?php endif; ?>
</div>