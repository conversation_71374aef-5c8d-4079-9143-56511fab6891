<?php
$footer_copyright = get_field('footer_copyright', 'option');
?>

<footer id="footer">
    <div class="container">
        <div class="column legal-column">
            <a class="logo-link" href="/">
                <?php echo lazyload(asset_url('images/logos/logo-alt.png'), get_bloginfo('name') . ' logo', 'logo'); ?>
            </a>

            <?php
            if (exists($footer_copyright)) {
                echo '<div class="legal-text">' . $footer_copyright . '</div>';
            }
            ?>

            <?php component('local/menus/footer-legal-menu'); ?>
        </div>

        <div class="column office-column">
            <p class="column-header">Our Office</p>
            <?php component('local/other/footer-office'); ?>
            <?php
            $conveyancing_number = get_field('footer_conveyancing_number', 'options');

            if (exists($conveyancing_number)) : ?>
                <p>Residential conveyancing: <?php echo '<a href="tel:' . spaceless($conveyancing_number) . '" itemprop="telephone">' . $conveyancing_number . '</a>'; ?></p>
            <?php endif; ?>
        </div>

        <div class="column company-column">
            <p class="column-header">Company</p>
            <?php component('local/menus/footer-menu'); ?>
        </div>

        <div class="column social-column">
            <!-- Start of SRA Digital Badge code -->
            <div style="max-width:200px;max-height:163px;">
                <div style="position: relative;padding-bottom: 59.1%;height: auto;overflow: hidden;">
                    <iframe title="SRA Badge" class="lazy" data-src="https://cdn.yoshki.com/iframe/55845r.html" frameborder="0" scrolling="no" allowTransparency="true" style="border:0px; margin:0px; padding:0px; backgroundColor:transparent; top:0px; left:0px; width:100%; height:100%; position: absolute;"></iframe>
                </div>
            </div>
            <!-- End of SRA Digital Badge code -->

            <?php
            $sra_statement = get_field('footer_sra_statement', 'options');

            if (exists($sra_statement)) : ?>
                <p class="sra-statement"><?php echo $sra_statement; ?></p>
            <?php endif; ?>
        </div>
    </div>
</footer>