<?php

/**
 * Lets split out the functions file.
 * 
 */
$roots_includes = array(
	'/modules/Helpers.php',
	'/modules/Site.php',
	'/modules/Menus.php',
	'/modules/Shortcodes.php',
	'/modules/Capabilities.php'
);

foreach ($roots_includes as $file) {
	if (!$filepath = locate_template($file)) {
		trigger_error("Error locating `$file` for inclusion.", E_USER_ERROR);
	}

	require_once $filepath;
}
unset($file, $filepath);

/**
 * Add our JS files
 * 
 */
add_action('wp_enqueue_scripts', 'site_scripts');
function site_scripts()
{
	wp_enqueue_script('main-js', asset_url('js/main.min.js'), array(), filemtime(get_stylesheet_directory() . '/assets/js/main.min.js'), true);
	wp_enqueue_script('jquery-js', "https://ajax.googleapis.com/ajax/libs/jquery/3.7.0/jquery.min.js", true);
	wp_enqueue_script('swiper-js', "https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js", true);
}

/**
 * Add our CSS files
 * 
 */
add_action('wp_enqueue_scripts', 'site_styles');
function site_styles()
{
	wp_enqueue_style('main-css', asset_url('css/main.min.css'), array(), filemtime(get_stylesheet_directory() . '/assets/css/main.min.css'));
	wp_enqueue_style('print-css', asset_url('css/print.min.css'), array(), filemtime(get_stylesheet_directory() . '/assets/css/print.min.css'), 'print');
	wp_enqueue_style('swiper-css', "https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css");
}

/**
 * Adds defer="defer" to the main.min.js file
 * 
 */
add_filter('script_loader_tag', 'add_defer_attribute', 10, 2);
function add_defer_attribute($tag, $handle)
{
	if ('main-js' !== $handle) {
		return $tag;
	}
	return str_replace(' src', ' defer="defer" src', $tag);
}

/**
 * Change Login page logo
 * 
 */
function login_style_overide()
{
	wp_register_style('login-overide', get_theme_file_uri() . '/assets/css/admin.min.css');
	wp_enqueue_style('login-overide');
}
add_action('login_enqueue_scripts', 'login_style_overide', 100);
