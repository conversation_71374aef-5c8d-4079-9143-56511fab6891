<?php

// Ternary Operator
$musictype = ( 'jazz' === $music ) ? 'cool' : 'blah';

// Single line PHP tags
<input name="<?php echo esc_attr( $name ); ?>" />

// Interpolation
do_action( "{$new_status} and {$post->post_type}" );

// Set variable before the if
$data = $wpdb->get_var( '...' );
if ( $data ) {
    // do something...
}

// If (Yoda style)
if ( true === $the_force ) {
    // do something...
}

// If Ternary
$musictype = ( 'jazz' === $music ) ? 'cool' : 'blah';

// If else
if ( true === $the_force ) {
    // do something...
} elseif ( true === $the__star_force ) {
    // do something...
} else {
    // do something...
}

// If exists
if ( ! empty() == $the_force ) {
    // do something...
}

// Foreach
foreach ( $items as $item ) {
    // do something...
}

// Function
function foo( $args, $default = 'something' ) {
    // do something...
}