var exists=function(e){return $(e).length>0};!function(){var e=$("#mobile-menu-trigger"),o=$("#mobile-menu-panel");exists(e)&&e.on("click touch",(function(){$(this).toggleClass("menu-open"),o.fadeToggle("fast")})),$(window).on("load resize",(function(){$(window).width()>960&&(e.removeClass("menu-open"),o.fadeOut("fast"))})).resize();var t=$("#menu-mobile-menu").find(".menu-item-has-children");t.prepend('<span class="sub-menu-trigger"><span class="sub-menu-trigger-icon">+</span></span>'),t.on("click touch",(function(){console.log("clicked"),$(this).find(".sub-menu").slideToggle("fast","linear"),$(this).toggleClass("sub-menu-open")}))}(),function(){var e=$("#sidebar").find(".wpcf7-form"),o=e.find(".form-expand");if(o.hide(),exists(e)){e.prepend('<div class="form-close" id="form-close" aria-label="Close the expanded form">\n                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">\n                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />\n                        </svg>\n                    </div>');var t=e.find("#form-close");e.find(".field-row").on("click",(function(){o.slideDown("fast"),t.show()})),t.on("click",(function(){o.slideUp("fast"),$(this).hide()}))}}();var setCookie=function(e,o,t){var n=new Date;n.setTime(n.getTime()+24*t*60*60*1e3);var i="expires="+n.toUTCString();document.cookie=escape(e)+"="+escape(o)+";"+i+";path=/;secure;"},getCookie=function(e){for(var o=unescape(e)+"=",t=document.cookie.split(";"),n=0;n<t.length;n++){for(var i=t[n];" "==i.charAt(0);)i=i.substring(1);if(0==i.indexOf(o))return i.substring(o.length,i.length)}return""},RGBToHex=function(e){var o=e.indexOf(",")>-1?",":" ",t=(+(e=e.substr(4).split(")")[0].split(o))[0]).toString(16),n=(+e[1]).toString(16),i=(+e[2]).toString(16);return 1==t.length&&(t="0"+t),1==n.length&&(n="0"+n),1==i.length&&(i="0"+i),"#"+t+n+i},createModal=function(e,o){var t='\n    <div class="modal '.concat(o,'" id="modal" aria-modal="true">\n      <div class="modal-inner">\n        <button class="modal-close" id="modal-close" aria-label="Close modal">\n          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">\n            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />\n          </svg>\n        </button>\n        <div class="modal-content">\n          ').concat(e,"\n        </div>\n      </div>\n    </div>\n  ");$("body").append(t),$("body").find("#modal").addClass("active")},closeModal=function(){var e=$("body").find("#modal");exists(e)&&($("body").find("#overlay").removeClass("active"),e.removeClass("active"),e.remove())};!function(){var e=$("body").find('[data-trigger="modal"]'),o=$("body").find("#overlay");exists(e)&&e.on("click",(function(e){if(e.preventDefault(),exists(o)){o.addClass("active");var t=$(this).attr("href");if(t){var n=$(this).data("referrer")||window.location.protocol+"//"+window.location.host+"/"+window.location.pathname,i=$(this).data("theme")?"modal-"+$(this).data("theme"):"modal";createModal('<iframe class="iframe-modal" src="'.concat(t,"?modal=true&theme=").concat(i,"&referrer=").concat(n,'"></iframe>'),i)}else if($(this).data("target")){var a=$(this).data("target"),s=$("body").find("#"+a).html();createModal(s)}else{var r=$(this).html();createModal(r)}}$("html, body").animate({scrollTop:$("body").offset().top-50},500)})),closeModal(),$(document).on("click","#overlay",(function(){closeModal()})),$(document).on("click","#modal-close",(function(){closeModal()})),$(document).on("keyup",(function(e){27==e.keyCode&&closeModal()}))}(),function(){var e=$("body").find('[data-trigger="cookies"]'),o=$("body").find("#cookie-modal"),t=$("body").find("#cookie-bar"),n=getCookie("wordpress_cookiepref"),i=o.find("#ga-cookie-switch"),a=o.find("#tp-cookie-switch"),s=$("body").find(".custom-cookies"),r=$("body").find(".reject-cookies"),l=$("body").find(".allow-cookies"),c=$("body").find("#overlay");window.dataLayer=window.dataLayer||[],exists(e)&&e.on("click touch",(function(e){e.preventDefault(),$("body").find(c).addClass("active"),t.hide(),o.show(),$("html, body").animate({scrollTop:$("body").offset().top-50},500)})),$("#footer").find("#menu-item-174").on("click touch",(function(e){e.preventDefault(),t.show()})),null==n||null==n||""==n?(t.show(),i.prop("checked",!0),a.prop("checked",!0)):"all"==n?(i.prop("checked",!0),a.prop("checked",!0),t.hide()):"ga"==n?(i.prop("checked",!0),a.prop("checked",!1),t.hide()):"tp"==n?(i.prop("checked",!1),a.prop("checked",!0),t.hide()):"none"==n&&(i.prop("checked",!1),a.prop("checked",!1),t.hide()),"all"==n?(window.dataLayer.push({event:"all-cookies-allowed"}),console.log("Cookie permissions: All cookies allowed")):"ga"==n?(window.dataLayer.push({event:"ga-cookies-allowed"}),console.log("Cookie permissions: Google Analytics cookies allowed")):"tp"==n?(window.dataLayer.push({event:"tp-cookies-allowed"}),console.log("Cookie permissions: Third Party cookies allowed")):"none"==n?(window.dataLayer.push({event:""}),console.log("Cookie permissions: Essential cookies only")):(window.dataLayer.push({event:""}),console.log("Cookie permissions: Cookies not consented yet")),l.on("click touch",(function(e){e.preventDefault(),setCookie("wordpress_cookiepref","all",365),t.hide(),closeModal(),location.reload()})),s.on("click touch",(function(e){e.preventDefault(),i.prop("checked")&&a.prop("checked")?setCookie("wordpress_cookiepref","all",365):i.prop("checked")&&!a.prop("checked")?setCookie("wordpress_cookiepref","ga",365):a.prop("checked")&&!i.prop("checked")?setCookie("wordpress_cookiepref","tp",365):setCookie("wordpress_cookiepref","none",365),t.hide(),closeModal(),location.reload()})),r.on("click touch",(function(e){e.preventDefault(),setCookie("wordpress_cookiepref","none",365),t.hide(),closeModal(),location.reload()}))}(),function(){var e=document.getElementById("map");if(exists(e)){var o=RGBToHex($("body").find(".map").css("fill")),t=$("body").find(".map-meta"),n=new Array;t.each((function(e){var o=$(this).find(".office-name").text(),t=$(this).find(".office-address").text(),i=$(this).find(".lat").text(),a=$(this).find(".lon").text(),s='<div id="map-meta-content"><p class="map-meta-title">'+o+'</p><ul class="map-meta-popup"><li><p>'+t+'</p></li><li><a href="https://maps.google.co.uk?q='+t+'" target="_blank" rel="noreferrer">View on Google</a></li><li><a href="https://maps.google.co.uk?q=current location to'+t+'" target="_blank" rel="noreferrer">Get Directions</a></li></ul></div>';n.push([s,i,a,e++])}));var i,a,s=new google.maps.Map(e,{mapTypeId:google.maps.MapTypeId.ROADMAP,featureType:"poi",styles:[{featureType:"poi",stylers:[{visibility:"off"}]},{stylers:[{hue:o},{saturation:-20}]}]}),r=new google.maps.LatLngBounds,l=new google.maps.InfoWindow;for(a=0;a<n.length;a++)i=new google.maps.Marker({position:new google.maps.LatLng(n[a][1],n[a][2]),map:s}),r.extend(i.position),google.maps.event.addListener(i,"click",function(e,o){return function(){l.setContent(n[o][0]),l.open(s,e)}}(i,a));t.length>1?s.fitBounds(r):(s.setZoom(17),s.setCenter(new google.maps.LatLng(n[0][1],n[0][2])))}}(),function(){function e(e){var o=(e=e||{}).lazyClass||"lazy",t=e.lazyBackgroundClass||"lazy-bg",n="idleLoadTimeout"in e?e.idleLoadTimeout:200,i=e.observeChanges||!1,a=e.events||{},s=e.noPolyfill||!1,r=window,l="requestIdleCallback",c="IntersectionObserver",d=c in r&&"".concat(c,"Entry")in r,p=/baidu|(?:google|bing|yandex|duckduck)bot/i.test(navigator.userAgent),f=["srcset","src","poster"],u=[],h=function(e,n){return u.slice.call((n||document).querySelectorAll(e||"img.".concat(o,",video.").concat(o,",iframe.").concat(o,",.").concat(t)))},m=function(o){var n=o.parentNode;"PICTURE"==n.nodeName&&k(h("source",n),g),"VIDEO"==o.nodeName&&k(h("source",o),g),g(o);var i=o.classList;i.contains(t)&&(i.remove(t),i.add(e.lazyBackgroundLoaded||"lazy-bg-loaded"))},v=function(e){for(var o in a)e.addEventListener(o,a[o].listener||a[o],a[o].options||void 0)},g=function(e){for(var t in f)if(f[t]in e.dataset){e.setAttribute(f[t],e.dataset[f[t]]);var n=e.parentNode;"SOURCE"===e.nodeName&&n.autoplay&&(n.load(),/Trident/.test(navigator.userAgent)&&n.play(),n.classList.remove(o)),e.classList.remove(o)}},k=function(e,o){for(var t=0;t<e.length;t++)r[c]&&o instanceof r[c]?o.observe(e[t]):o(e[t])},y=h();if(k(y,v),d&&!p){var w=new r[c]((function(e){k(e,(function(e){if(e.isIntersecting||e.intersectionRatio){var o=e.target;l in r&&n?r[l]((function(){m(o)}),{timeout:n}):m(o),w.unobserve(o),(y=y.filter((function(e){return e!=o}))).length||i||w.disconnect()}}))}),{rootMargin:"".concat("threshold"in e?e.threshold:200,"px 0%")});k(y,w),i&&k(h(e.observeRootSelector||"body"),(function(o){new MutationObserver((function(){k(h(),(function(e){y.indexOf(e)<0&&(y.push(e),v(e),d&&!p?w.observe(e):(s||p)&&k(y,m))}))})).observe(o,e.mutationObserverOptions||{childList:!0,subtree:!0})}))}else(s||p)&&k(y,m)}document.addEventListener("DOMContentLoaded",e),$(document).on("sf:ajaxfinish",".searchandfilter",(function(){e()}))}(),function(){var e=$("body").find("#emergency-popup"),o=localStorage.getItem("emergencypopup"),t=$("body").find("#overlay");if(exists(e))if(null===o){var n=e.html();createModal(n,"emergency"),$("body").find(t).addClass("active"),$("html, body").animate({scrollTop:$("body").offset().top-50},500),$("body").find(".emergency").find(".modal-close").on("click",(function(e){e.preventDefault(),localStorage.setItem("emergencypopup",!0),closeModal()}))}else e.hide(),$("body").find(t).removeClass("active"),closeModal()}(),function(){var e=$("body").find(".email");exists(e)&&e.each((function(){var e=$(this).attr("href");if($(this).attr("href","mailto:".concat(atob(e))),$(this).hasClass("has-icon")){var o=$(this).find(".icon");console.log(o),$(this).text(" "+atob(e)),$(this).prepend(o)}else $(this).text(atob(e))}))}(),function(){var e=$(".main-content").find(".expander");if(exists(e)){var o=e.find(".expander-trigger"),t=e.find(".expander-content");o.each((function(){var e=$(this),o=e.attr("data-expander");e.on("click",(function(n){n.preventDefault(),e.hasClass("active")?(t.closest("#"+o).slideUp("fast"),e.removeClass("active")):(t.closest("#"+o).slideToggle("fast"),e.addClass("active"))}))}))}}(),new Swiper(".logo-rotator",{autoplay:{delay:3e3},slidesPerView:1,spaceBetween:20,slidesPerGroup:1,pagination:{el:".swiper-pagination",clickable:!0},breakpoints:{480:{slidesPerView:2,slidesPerGroup:2,spaceBetween:20},760:{slidesPerView:4,slidesPerGroup:4,spaceBetween:20}}}),function(){var e=$("#site-search"),o=$("#search-bar"),t=$("body").find("#overlay");e.on("click touch",(function(){o.toggleClass("active"),e.toggleClass("active"),exists(t)&&(t.toggleClass("active"),t.toggleClass("search-overlay"))})),$(document).on("click","#overlay",(function(){o.hasClass("active")&&(o.removeClass("active"),e.removeClass("active"),t.removeClass("active"),t.removeClass("search-overlay"))}))}(),function(){var e=$("body").find("#share-button");exists(e)&&e.on("click touch",(function(){$("body").find(".share-link-content").find(".share-copy").on("click touch",(function(){var e=$("<input>"),o=$(location).attr("href");$(this).append(e),e.val(o).select(),document.execCommand("copy"),e.remove(),console.log("Link copied");var t=$(this).text();$(this).text("Copied!"),setTimeout((function(){$(this).text(t)}),1e3)}))}))}(),new Swiper("#git-cta-rotator",{loop:!0,autoplay:{delay:7e3},pagination:{el:".swiper-pagination",clickable:!0}});